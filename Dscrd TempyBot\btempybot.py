import discord
from discord.ext import commands
from discord import ui
import time
from collections import defaultdict
import requests
from discord.ui import <PERSON><PERSON>, View
import asyncio

# Bot setup
intents = discord.Intents.default()
intents.message_content = True
intents.members = True
intents.voice_states = True
bot = commands.Bot(command_prefix=".v ", intents=intents)

# Dictionary to store rename usage: {user_id: {'count': int, 'reset_time': float}}
rename_usage = defaultdict(lambda: {'count': 0, 'reset_time': 0})

# Constants
MAX_RENAMES = 2
COOLDOWN_DURATION = 600  # 10 minutes in seconds

# Store channel owners and bot-created channels
channel_owners = {}
channel_managers = {}
bot_created_channels = set()
# Track welcome messages to edit them when appropriate
welcome_messages = {}

# Cooldown system
user_cooldowns = {}  # Format: {user_id: {'last_commands': [timestamp1, timestamp2, ...], 'on_cooldown_until': timestamp}}
COOLDOWN_THRESHOLD = 4  # Number of commands before triggering cooldown
COOLDOWN_PERIOD = 270  # Cooldown duration in seconds (270s = 4 minutes and 30 seconds)
COMMAND_WINDOW = 60  # Time window to count commands (in seconds)

# Channel for logging voice activity
VOICE_LOG_CHANNEL_ID = 1345865496230236322  # Replace with your actual log channel ID
TARGET_CATEGORY_NAME = "● :::: ➕ Make Your Room ➕ :::: ●"
TOKEN = "MTI5ODY0OTQzNjQxMDYxMzg5MQ.GgY02q.fcusPxEfeimbjJrTT0pn1BTIWYsIAH7T6b_O6A"
FOUNDER_ID = 334316627099123713 # Replace with your actual Discord user ID
SERVER_NAME = "𝐃𝐞𝐚𝐭𝐡𝐙𝐨𝐧𝐞 𝐂𝐨𝐦𝐦𝐮𝐧𝐢𝐭𝐲"  # Replace with your actual server name


# TEST SPAM UNKNOWN

# Unknown command spam protection
unknown_command_cooldowns = {}  # Format: {user_id: cooldown_end_timestamp}
unknown_command_attempts = defaultdict(int)  # Format: {user_id: attempt_count}

# Constants for unknown command handling
UNKNOWN_COMMAND_COOLDOWN = 7200  # 1 hour in seconds
MAX_UNKNOWN_ATTEMPTS = 4  # Maximum unknown commands before cooldown

def is_user_on_unknown_cooldown(user_id):
    """Check if user is on cooldown for unknown commands"""
    if user_id not in unknown_command_cooldowns:
        return False
    
    current_time = time.time()
    cooldown_end = unknown_command_cooldowns[user_id]
    
    if current_time >= cooldown_end:
        # Cooldown expired, clean up
        del unknown_command_cooldowns[user_id]
        if user_id in unknown_command_attempts:
            del unknown_command_attempts[user_id]
        return False
    
    return True

def put_user_on_unknown_cooldown(user_id):
    """Put user on cooldown for unknown commands"""
    unknown_command_cooldowns[user_id] = time.time() + UNKNOWN_COMMAND_COOLDOWN
    unknown_command_attempts[user_id] = 0

async def handle_unknown_command(message, command_name):
    """Handle unknown command attempts with cooldown system"""
    user_id = message.author.id
    
    # Check if user is already on cooldown
    if is_user_on_unknown_cooldown(user_id):
        cooldown_end = unknown_command_cooldowns[user_id]
        time_left = int((cooldown_end - time.time()) / 60)  # Convert to minutes
        
        embed = discord.Embed(
            title="<:time:1257454120739016764> Command Cooldown Active",
            description=f"You are on cooldown for spamming Prefix.\n**Time remaining:** {time_left} minutes",
            color=0xff6b6b  # Red color
        )
        embed.set_footer(text="Wait 2 hours before using any commands.")
        
        await message.reply(embed=embed, mention_author=False)
        return
    
    # Increment unknown command attempts
    unknown_command_attempts[user_id] += 1
    current_attempts = unknown_command_attempts[user_id]
    
    # Check if user exceeded max attempts
    if current_attempts >= MAX_UNKNOWN_ATTEMPTS:
        put_user_on_unknown_cooldown(user_id)
        
        embed = discord.Embed(
            title="<:time:1257454120739016764> Command Cooldown Applied",
            description="You are on cooldown for **2 hours** for spamming bot prefix `.v`.",
            color=0xff4757  # Dark red color
        )
        embed.add_field(
            name="📋 How to view commands",
            value="Use `.v cmd` to show the list of available commands",
            inline=False
        )
        embed.add_field(
            name="<:deathzone:1250199513550028956>  !",
            value="Sorry you cannot use **any** command while on cooldown",
            inline=False
        )
        embed.set_footer(text="Try again after 2 hours")
        
        await message.reply(embed=embed, mention_author=False)
        print(f"User {message.author} ({user_id}) got 2-hours cooldown for prefix spam")
    else:
        # Warning message
        remaining_attempts = MAX_UNKNOWN_ATTEMPTS - current_attempts
        plural = "s" if remaining_attempts != 1 else ""
        
        embed = discord.Embed(
            title="<:deathzone:1250199513550028956> Oops, Unknown Command",
            description="The command is not in the command list.",
            color=0xffa726  # Orange color
        )
        embed.add_field(
            name="📋 How to view commands ?",
            value="Use `.v cmd` to show available commands",
            inline=False
        )
        embed.add_field(
            name="<:deathzone:1250199513550028956> Warning",
            value=f"Please don't spam bot prefix  `.v` **{remaining_attempts}** time{plural} more gets you a **2-hours cooldown**",
            inline=False
        )
        embed.set_footer(text=f"Attempt {current_attempts}/{MAX_UNKNOWN_ATTEMPTS}")
        
        await message.reply(embed=embed, mention_author=False)
        print(f"Prefix spam: .v {command_name} by {message.author} ({current_attempts}/{MAX_UNKNOWN_ATTEMPTS})")

# Add this event handler to catch all messages and handle unknown commands
@bot.event
async def on_message(message):
    # Don't respond to bots
    if message.author.bot:
        return
    
    # Check if message starts with .v prefix
    if message.content.startswith('.v '):
        # Check if user is on cooldown for ANY command usage
        if is_user_on_unknown_cooldown(message.author.id):
            cooldown_end = unknown_command_cooldowns[message.author.id]
            time_left = int((cooldown_end - time.time()) / 60)
            
            embed = discord.Embed(
                title="🚫 No commands for you",
                description="Sorry but you cannot use commands while on cooldown.",
                color=0xff4757  # Dark red color
            )
            embed.add_field(
                name="<:time:1257454120739016764> Time Remaining",
                value=f"{time_left} minutes",
                inline=True
            )
            embed.add_field(
                name="📋 Reason",
                value="Prefix Spam `.v`",
                inline=True
            )
            embed.set_footer(text="Please wait for cooldown to expire")
            
            await message.reply(embed=embed, mention_author=False)
            return
        
        # Extract command name
        args = message.content[3:].strip().split()
        if not args:
            return
        
        command_name = args[0].lower()
        
        # List of your valid commands (update this with all your actual commands)
        valid_commands = [
            'lock', 'unlock', 'vcinfo', 'limit', 'rename', 'renames', 'status', 
            'perm', 'reject', 'tmute', 'tunmute', 'man add', 'man remove', 'transfer',
            'claim', 'owner', 'request', 'chatdlt', 'sbon', 'sboff', 'cmd', 'manager', 'camon', 'camoff' # Add  commands
            'cldcheck', 'cldclr', 'camon', 'camoff' # Add admin commands
            # Add all your actual command names here
        ]
        
        # Check if command is valid
        if command_name not in valid_commands:
            await handle_unknown_command(message, command_name)
            return
    
    # Process commands normally
    await bot.process_commands(message)


# Function to log voice channel creation
async def log_voice_channel_creation(channel, owner):
    log_channel = bot.get_channel(VOICE_LOG_CHANNEL_ID)
    if not log_channel:
        return  # Log channel not found
    
    # Gather channel information
    channel_name = channel.name
    created_time = discord.utils.utcnow().strftime("%d/%m/%Y %H:%M:%S")
    channel_limit = channel.user_limit if channel.user_limit else "0"
    
    # Get current members in voice
    in_voice_users = [member for member in channel.members]
    
    # Get managers from your tracking dictionary
    managers = []
    if channel.id in channel_managers:
        managers = [bot.get_user(user_id) for user_id in channel_managers[channel.id]]
    
    # Get permitted/rejected users (you might need to implement this tracking)
    permitted_users = []  # Implement based on your permission system
    rejected_users = []   # Implement based on your permission system
    
    # Create the embed
    embed = discord.Embed(
        title=f"{channel_name} - Voice Info",
        color=0x36393F
    )
    
    # Add owner field with crown emoji
    embed.add_field(
        name="👑 `Owner`",
        value=f"{owner.mention}\n{owner.display_name}",
        inline=False
    )
    
    # Add channel status info
    embed.add_field(name="🔊 Channel Status", value="Open", inline=True)
    embed.add_field(name="⏳ Created", value=f"{created_time}", inline=True)
    embed.add_field(name="👥 Channel Limit", value=channel_limit, inline=True)
    
    # Add "In Voice" field
    in_voice_text = "\n".join([m.mention for m in in_voice_users]) or "None"
    embed.add_field(
        name=f"🎧 `In Voice` ({len(in_voice_users)})",
        value=in_voice_text,
        inline=False
    )
    
    # Add managers
    managers_text = "\n".join([m.mention for m in managers if m]) or "None"
    embed.add_field(
        name="✨ `Managers`",
        value=managers_text,
        inline=False
    )
    
    # Add permitted users (placeholder - implement based on your system)
    permitted_text = "None"  # Replace with actual permitted users
    embed.add_field(
        name="🟢 Permitted Users",
        value=permitted_text,
        inline=False
    )
    
    # Add rejected users (placeholder - implement based on your system)
    rejected_text = "None"  # Replace with actual rejected users
    embed.add_field(
        name="🔴 Rejected Users",
        value=rejected_text,
        inline=False
    )
    
    # Set thumbnail to owner's avatar
    embed.set_thumbnail(url=owner.display_avatar.url)
    
    # Send the embed to the logging channel
    await log_channel.send(embed=embed)

@bot.event
async def on_command_error(ctx, error):
    # Check for command not found error
    if isinstance(error, commands.CommandNotFound):
        # Extract the attempted command from the message
        try:
            # Get the attempted command from the message content
            # Assuming messages start with a period and the command follows
            message_content = ctx.message.content
            attempted_command = message_content.split()[1] if message_content.startswith('.v') else "unknown"
            
            # Create an embed for the error message
            embed = await create_response_embed(
                "Command Not Found", 
                f"The command `{attempted_command}` is not unlisted.\n\nUse `.v cmd` to see listed commands."
            )
            await ctx.reply(embed=embed, mention_author=False)
        except Exception as e:
            # Fallback if we can't extract the command for some reason
            embed = await create_response_embed(
                "Command Not Found", 
                f"That command is not recognized.\n\nUse `.v cmd` to see a list of available commands."
            )
            await ctx.reply(embed=embed, mention_author=False)
    
    # Handle other types of errors
    elif isinstance(error, commands.MissingRequiredArgument):
        parameter = error.param.name
        embed = await create_response_embed(
            "Missing Argument", 
            f"The `{parameter}` argument is required for this command."
        )
        await ctx.reply(embed=embed, mention_author=False)
    
    elif isinstance(error, commands.BadArgument):
        embed = await create_response_embed(
            "Invalid Argument", 
            "One or more arguments are invalid. Please check your command syntax."
        )
        await ctx.reply(embed=embed, mention_author=False)
    
    # Add more error types as needed
    
    # For debugging purposes, print the error to console
    print(f"Command error: {error}")

# Function to check and update cooldowns
async def check_cooldown(ctx):
    user_id = ctx.author.id
    current_time = time.time()
    
    # Initialize user in cooldown dictionary if not present
    if user_id not in user_cooldowns:
        user_cooldowns[user_id] = {'last_commands': [], 'on_cooldown_until': 0}
    
    # Check if user is currently on cooldown
    if user_cooldowns[user_id]['on_cooldown_until'] > current_time:
        remaining_time = int(user_cooldowns[user_id]['on_cooldown_until'] - current_time)
        embed = await create_response_embed("Cooldown Active", f"{ctx.author.mention} You're on cooldown! Please wait {remaining_time} seconds.")
        await ctx.reply(embed=embed, mention_author=False)
        return True
    
    # Update command history, keeping only recent commands
    user_cooldowns[user_id]['last_commands'] = [
        timestamp for timestamp in user_cooldowns[user_id]['last_commands'] 
        if current_time - timestamp < COMMAND_WINDOW
    ]
    
    # Add current command timestamp
    user_cooldowns[user_id]['last_commands'].append(current_time)
    
    # Check if threshold is reached
    if len(user_cooldowns[user_id]['last_commands']) >= COOLDOWN_THRESHOLD:
        # Set cooldown
        user_cooldowns[user_id]['on_cooldown_until'] = current_time + COOLDOWN_PERIOD
        user_cooldowns[user_id]['last_commands'] = []  # Clear command history
        embed = await create_response_embed("Cooldown Triggered", f"{ctx.author.mention} You're on cooldown for {COOLDOWN_PERIOD} seconds.")
        await ctx.reply(embed=embed, mention_author=False)
        return True
    
    return False  # Not on cooldown

# Helper function to create a mock context for our existing commands
async def create_mock_context(interaction, command_name, *args):
    class MockContext:
        def __init__(self, interaction, command_name):
            self.author = interaction.user
            self.guild = interaction.guild
            self.channel = interaction.channel
            self.bot = interaction.client
            self.command = command_name
            self.voice_client = None
            self.message = None
            
            # Set up voice client
            if self.author.voice:
                self.voice_client = self.author.voice
            
        async def reply(self, content=None, embed=None, mention_author=False):
            # For commands that don't respond directly through the interaction
            if isinstance(interaction.response, discord.InteractionResponse) and not interaction.response.is_done():
                await interaction.response.send_message(content=content, embed=embed, ephemeral=True)
            else:
                await interaction.followup.send(content=content, embed=embed, ephemeral=True)
                
    ctx = MockContext(interaction, command_name)
    
    # Add send method directly to the instance
    async def send(content=None, embed=None):
        if isinstance(interaction.response, discord.InteractionResponse) and not interaction.response.is_done():
            await interaction.response.send_message(content=content, embed=embed, ephemeral=True)
        else:
            await interaction.followup.send(content=content, embed=embed, ephemeral=True)
    
    ctx.send = send
    
    return ctx

# Function to handle button commands
async def handle_button_command(interaction, command_name):
    # Check if the user is in a voice channel
    if not interaction.user.voice or not interaction.user.voice.channel:
        await interaction.response.send_message("You need to be in a voice channel to use this command!", ephemeral=True)
        return
        
    channel = interaction.user.voice.channel
    
    # Create a mock context for our existing commands
    ctx = await create_mock_context(interaction, command_name)
    
    # Check if user is on active cooldown, but don't track button presses for cooldown accumulation
    user_id = interaction.user.id
    current_time = time.time()
    
    # Initialize user in cooldown dictionary if not present
    if user_id not in user_cooldowns:
        user_cooldowns[user_id] = {'last_commands': [], 'on_cooldown_until': 0}
    
    # Only check if user is currently on cooldown, don't increment the count
    if user_cooldowns[user_id]['on_cooldown_until'] > current_time:
        remaining_time = int(user_cooldowns[user_id]['on_cooldown_until'] - current_time)
        embed = await create_response_embed("Cooldown Active", f"{interaction.user.mention} You're on cooldown! Please wait {remaining_time} seconds.")
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return
        
    # Execute the appropriate command
    if command_name == "lock":
        await lock(ctx)
    elif command_name == "unlock":
        await unlock(ctx)
    elif command_name == "info":
        await info(ctx)
    
    # Acknowledge the interaction if needed
    if isinstance(interaction.response, discord.InteractionResponse) and not interaction.response.is_done():
        await interaction.response.defer()

# Define custom View with emoji-only buttons for voice channel management
class VoiceControlPanel(ui.View):
    def __init__(self, channel_id):
        super().__init__(timeout=None)  # Make buttons persistent
        self.channel_id = channel_id
    
    @ui.button(emoji="<:Locked:1338041296518123531>", style=discord.ButtonStyle.secondary)
    async def lock_button(self, interaction: discord.Interaction, button: ui.Button):
        await handle_button_command(interaction, "lock")
    
    @ui.button(emoji="<:Unlocked:1338041281296728064>", style=discord.ButtonStyle.secondary)
    async def unlock_button(self, interaction: discord.Interaction, button: ui.Button):
        await handle_button_command(interaction, "unlock")

    @ui.button(emoji="<:OwnerCrown:1338041249025884191>", style=discord.ButtonStyle.secondary)
    async def managers_button(self, interaction: discord.Interaction, button: ui.Button):
        # Open a select menu for manager options
        await interaction.response.send_message("Manager options:", view=ManagerOptionsView(self.channel_id), ephemeral=True)
    
    @ui.button(emoji="ℹ️", style=discord.ButtonStyle.secondary)
    async def info_button(self, interaction: discord.Interaction, button: ui.Button):
        await handle_button_command(interaction, "info")
    
    @ui.button(emoji="<:Checkmark:1338041264565784586>", style=discord.ButtonStyle.secondary)
    async def permit_button(self, interaction: discord.Interaction, button: ui.Button):
        # Open a modal to get the user to permit
        modal = PermitUserModal(self.channel_id)
        await interaction.response.send_modal(modal)
    
    @ui.button(emoji="<:nope:1338041312133386300>", style=discord.ButtonStyle.secondary)
    async def reject_button(self, interaction: discord.Interaction, button: ui.Button):
        # Open a modal to get the user to reject
        modal = RejectUserModal(self.channel_id)
        await interaction.response.send_modal(modal)
    
    @ui.button(emoji="<:GFX:1338041230998638603>", style=discord.ButtonStyle.secondary)
    async def rename_button(self, interaction: discord.Interaction, button: ui.Button):
        
        # Open a modal to rename the channel
        modal = RenameChannelModal(self.channel_id)
        await interaction.response.send_modal(modal)
    


# Modals for input collection
class PermitUserModal(ui.Modal, title="Permit User"):
    def __init__(self, channel_id):
        super().__init__()
        self.channel_id = channel_id
        
    user_id = ui.TextInput(label="User ID or @mention", placeholder="Enter user ID or @mention", required=True)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Extract user from input
        user_input = self.user_id.value
        user_id = None
        
        # Handle different input formats
        if user_input.startswith("<@") and user_input.endswith(">"):
            # It's a mention, extract the ID
            user_id = user_input.strip("<@!>")
        else:
            # Assume it's a raw ID
            user_id = user_input
        
        try:
            user_id = int(user_id)
            member = interaction.guild.get_member(user_id)
            
            if member:
                # Create a mock context for our existing permit command
                ctx = await create_mock_context(interaction, "permit", member)
                await permit(ctx, member)
                await interaction.response.send_message(f"Permitted {member.mention} to join the channel!", ephemeral=True)
            else:
                await interaction.response.send_message("Couldn't find that user. Please make sure they are in the server.", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Invalid user ID or mention. Please try again.", ephemeral=True)

class RejectUserModal(ui.Modal, title="Reject User"):
    def __init__(self, channel_id):
        super().__init__()
        self.channel_id = channel_id
        
    user_id = ui.TextInput(label="User ID or @mention", placeholder="Enter user ID or @mention", required=True)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Extract user from input
        user_input = self.user_id.value
        user_id = None
        
        # Handle different input formats
        if user_input.startswith("<@") and user_input.endswith(">"):
            # It's a mention, extract the ID
            user_id = user_input.strip("<@!>")
        else:
            # Assume it's a raw ID
            user_id = user_input
        
        try:
            user_id = int(user_id)
            member = interaction.guild.get_member(user_id)
            
            if member:
                # Create a mock context for our existing reject command
                ctx = await create_mock_context(interaction, "reject", member)
                await reject(ctx, member)
                await interaction.response.send_message(f"Rejected {member.mention} from the channel!", ephemeral=True)
            else:
                await interaction.response.send_message("Couldn't find that user. Please make sure they are in the server.", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Invalid user ID or mention. Please try again.", ephemeral=True)

class SetLimitModal(ui.Modal, title="Set Channel Limit"):
    def __init__(self, channel_id):
        super().__init__()
        self.channel_id = channel_id
        
    limit = ui.TextInput(label="User Limit", placeholder="Enter a number (0 for unlimited)", required=True)
    
    async def on_submit(self, interaction: discord.Interaction):
        try:
            limit_value = int(self.limit.value)
            if limit_value < 0:
                await interaction.response.send_message("Limit must be 0 or higher.", ephemeral=True)
                return
                
            # Create a mock context for our existing limit command
            ctx = await create_mock_context(interaction, "limit", limit_value)
            await limit(ctx, limit_value)
            await interaction.response.send_message(f"Channel limit set to {limit_value}!", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Please enter a valid number.", ephemeral=True)

class RenameChannelModal(ui.Modal, title="Rename Channel"):
    def __init__(self, channel_id):
        super().__init__()
        self.channel_id = channel_id
        
    new_name = ui.TextInput(label="New Channel Name", placeholder="Enter a new name for the channel", required=True)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Create a mock context for our existing name command
        ctx = await create_mock_context(interaction, "name", self.new_name.value)
        await name(ctx, new_name=self.new_name.value)
        await interaction.response.send_message(f"Channel renamed to {self.new_name.value}!", ephemeral=True)

# Manager options view with buttons
class ManagerOptionsView(ui.View):
    def __init__(self, channel_id):
        super().__init__(timeout=60)  # 1 minute timeout
        self.channel_id = channel_id
    
    @ui.button(label="Add Manager", style=discord.ButtonStyle.success, emoji="➕")
    async def add_manager_button(self, interaction: discord.Interaction, button: ui.Button):
        modal = AddManagerModal(self.channel_id)
        await interaction.response.send_modal(modal)
    
    @ui.button(label="Remove Manager", style=discord.ButtonStyle.danger, emoji="➖")
    async def remove_manager_button(self, interaction: discord.Interaction, button: ui.Button):
        modal = RemoveManagerModal(self.channel_id)
        await interaction.response.send_modal(modal)
    
    @ui.button(label="List Managers", style=discord.ButtonStyle.secondary, emoji="📋")
    async def list_managers_button(self, interaction: discord.Interaction, button: ui.Button):
        channel = interaction.guild.get_channel(self.channel_id)
        if not channel:
            await interaction.response.send_message("Channel not found!", ephemeral=True)
            return
            
        managers = channel_managers.get(self.channel_id, set())
        if not managers:
            await interaction.response.send_message("This channel has no managers.", ephemeral=True)
            return
            
        managers_list = '\n'.join([f"• {manager.mention} ({manager.name})" for manager in managers])
        embed = await create_response_embed("Channel Managers", managers_list)
        await interaction.response.send_message(embed=embed, ephemeral=True)

class AddManagerModal(ui.Modal, title="Add Manager"):
    def __init__(self, channel_id):
        super().__init__()
        self.channel_id = channel_id
        
    user_id = ui.TextInput(label="User ID or @mention", placeholder="Enter user ID or @mention", required=True)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Extract user from input
        user_input = self.user_id.value
        user_id = None
        
        # Handle different input formats
        if user_input.startswith("<@") and user_input.endswith(">"):
            # It's a mention, extract the ID
            user_id = user_input.strip("<@!>")
        else:
            # Assume it's a raw ID
            user_id = user_input
        
        try:
            user_id = int(user_id)
            member = interaction.guild.get_member(user_id)
            
            if member:
                # Create a mock context for our existing man add command
                ctx = await create_mock_context(interaction, "man", "add", member)
                await manage(ctx, "add", member)
                await interaction.response.send_message(f"Added {member.mention} as a manager!", ephemeral=True)
            else:
                await interaction.response.send_message("Couldn't find that user. Please make sure they are in the server.", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Invalid user ID or mention. Please try again.", ephemeral=True)

class RemoveManagerModal(ui.Modal, title="Remove Manager"):
    def __init__(self, channel_id):
        super().__init__()
        self.channel_id = channel_id
        
    user_id = ui.TextInput(label="User ID or @mention", placeholder="Enter user ID or @mention", required=True)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Extract user from input
        user_input = self.user_id.value
        user_id = None
        
        # Handle different input formats
        if user_input.startswith("<@") and user_input.endswith(">"):
            # It's a mention, extract the ID
            user_id = user_input.strip("<@!>")
        else:
            # Assume it's a raw ID
            user_id = user_input
        
        try:
            user_id = int(user_id)
            member = interaction.guild.get_member(user_id)
            
            if member:
                # Create a mock context for our existing man remove command
                ctx = await create_mock_context(interaction, "man", "remove", member)
                await manage(ctx, "remove", member)
                await interaction.response.send_message(f"{ctx.author.mention} **Removed** {member.mention} as a manager!", ephemeral=True)
            else:
                await interaction.response.send_message("Couldn't find that user. Please make sure they are in the server.", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Invalid user ID or mention. Please try again.", ephemeral=True)


async def create_response_embed(title, description, color=0x99AAB5):
    embed = discord.Embed(
        title=title,
        description=description,
        color=0x5E6161
    )
    if bot.user.avatar:
        embed.set_author(name=bot.user.name, icon_url=bot.user.avatar.url)
        embed.set_thumbnail(url=bot.user.avatar.url)
    else:
        embed.set_author(name=bot.user.name, icon_url=bot.user.default_avatar.url)
        embed.set_thumbnail(url=bot.user.default_avatar.url)

    return embed



async def is_channel_owner_or_manager(ctx) -> bool:
    if not ctx.author.voice or not ctx.author.voice.channel:
        embed = await create_response_embed("Error", "You need to be in a voice channel to use this command!")
        await ctx.reply(embed=embed, mention_author=False)
        return False
    
    channel = ctx.author.voice.channel
    owner = channel_owners.get(channel.id)
    managers = channel_managers.get(channel.id, set())
    
    if channel.id not in bot_created_channels:
        embed = await create_response_embed("Error", "Command works only in a `BɅD ZONE | Tempy` VC!")
        await ctx.reply(embed=embed, mention_author=False)
        return False
    
    # Allow if the user is the owner or a manager
    if owner == ctx.author or ctx.author in managers:
        return True
    else:
        embed = await create_response_embed("Error", "You are not the owner or a manager of this channel!")
        await ctx.reply(embed=embed, mention_author=False)
        return False

async def is_channel_owner(ctx) -> bool:
    if not ctx.author.voice or not ctx.author.voice.channel:
        embed = await create_response_embed("Error", "You need to be in a voice channel to use this command!")
        await ctx.reply(embed=embed, mention_author=False)
        return False
    
    channel = ctx.author.voice.channel
    owner = channel_owners.get(channel.id)
    
    if channel.id not in bot_created_channels:
        embed = await create_response_embed("Error", "Command works only in a `BɅD ZONE | Tempy` VC!")
        await ctx.reply(embed=embed, mention_author=False)
        return False
    
    # Allow only if the user is the owner
    if owner == ctx.author:
        return True
    else:
        embed = await create_response_embed("Error", "Only the channel owner can use this command!")
        await ctx.reply(embed=embed, mention_author=False)
        return False

# Checks if the target of a command is the owner (to prevent managers from affecting the owner)
async def check_not_targeting_owner(ctx, target_member):
    channel = ctx.author.voice.channel
    owner = channel_owners.get(channel.id)
    
    # If the target is the owner and the command issuer is not the owner
    if target_member == owner and ctx.author != owner:
        embed = await create_response_embed("Error", "You cannot use this command on the channel owner!")
        await ctx.reply(embed=embed, mention_author=False)
        return False
    return True



# Updated function to check if command was used in the target category
def is_in_target_category(ctx):
    # If in DM, return False
    if not ctx.guild:
        return False
   
    # Check if the text channel itself is in the target category
    if ctx.channel.category and ctx.channel.category.name == TARGET_CATEGORY_NAME:
        return True
   
    # Get the voice channel the message is related to
    voice_channel = None
   
    # If the message is directly in a voice channel
    if isinstance(ctx.channel, discord.VoiceChannel):
        voice_channel = ctx.channel
   
    # If this is the text chat of a voice channel
    elif hasattr(ctx.channel, 'related_voice_channel') and ctx.channel.related_voice_channel:
        voice_channel = ctx.channel.related_voice_channel
   
    # Check if we found a voice channel and if it's in the target category
    if voice_channel and voice_channel.category:
        if voice_channel.category.name == TARGET_CATEGORY_NAME:
            return True
   
    return False

# Example command implementation with category check
@bot.command()
@commands.check(is_in_target_category)
async def example(ctx):
    """Example command that only works in the target category"""
    await ctx.send("Command executed successfully!")

# Error handler for when commands are used in wrong category
@bot.event
async def on_command_error(ctx, error):
    if isinstance(error, commands.CheckFailure):
        # Optional: If you want to let users know they used the command in the wrong place
        # You can uncomment this, or leave it silent
        # await ctx.send(f"This command can only be used in the '{TARGET_CATEGORY_NAME}' category.")
        pass
    else:
        # Handle other errors as needed
        print(f"Error: {error}")


@bot.command(name='man')
@commands.check(is_in_target_category)
async def manage(ctx, action: str, member: discord.Member = None):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    if action.lower() == "add":
        # Only owner can add managers
        if not await is_channel_owner(ctx):
            return
        
        if not member or member.bot:
            embed = await create_response_embed("Error", "You must mention a valid user to add as a manager!")
            await ctx.reply(embed=embed, mention_author=False)
            return
        
        # Prevent adding the owner as a manager (redundant)
        channel = ctx.author.voice.channel
        owner = channel_owners.get(channel.id)
        if member == owner:
            embed = await create_response_embed("Error", "The channel owner cannot be added as a manager!")
            await ctx.reply(embed=embed, mention_author=False)
            return
        
        if channel.id not in channel_managers:
            channel_managers[channel.id] = set()
        
        channel_managers[channel.id].add(member)
        embed = await create_response_embed("Manager Added", f"👤 {ctx.author.mention} **Added** {member.mention} as manager of this channel!")
        await ctx.reply(embed=embed, mention_author=False)
    
    elif action.lower() == "remove":
        # Only owner can remove managers
        if not await is_channel_owner(ctx):
            return
        
        if not member:
            embed = await create_response_embed("Error", "You must mention a valid user to remove as a manager!")
            await ctx.reply(embed=embed, mention_author=False)
            return
        
        channel = ctx.author.voice.channel
        if channel.id in channel_managers and member in channel_managers[channel.id]:
            channel_managers[channel.id].remove(member)
            embed = await create_response_embed("Manager Removed", f"👤 {member.mention} is no longer a manager of this channel!")
            await ctx.reply(embed=embed, mention_author=False)
        else:
            embed = await create_response_embed("Error", f"{member.mention} is not a manager of this channel!")
            await ctx.reply(embed=embed, mention_author=False)
    
    else:
        embed = await create_response_embed("Error", "Invalid action! Use `.v man add @user` to add or" \
        "`.v man remove @user` to remove a manager.")
        await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='manager')
@commands.check(is_in_target_category)
async def show_managers(ctx):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    
    channel = ctx.author.voice.channel
    
    # Get the channel owner
    owner = channel_owners.get(channel.id)
    owner_text = f"👑 **Owner:** {owner.mention}" if owner else "👑 **Owner:** None"
    
    # Get the managers
    managers = channel_managers.get(channel.id, set())
    
    if managers:
        manager_list = "\n".join([f"👤 {manager.mention}" for manager in managers])
        managers_text = f"**Managers:**\n{manager_list}"
    else:
        managers_text = "**Managers:** None"
    
    # Create the embed
    embed_description = f"{owner_text}\n\n{managers_text}"
    embed = await create_response_embed("Channel Management", embed_description)
    await ctx.reply(embed=embed, mention_author=False)


# test request command 
@bot.command(name="request")
@commands.check(is_in_target_category)
async def request(ctx, target=None):
    # Removed the check that prevents users in voice channels from making requests
   
    if await check_cooldown(ctx):
        return
       
    if not target:
        embed = await create_response_embed("Missing Target", "Please mention the channel owner, provide a channel ID, or paste a channel link")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    owner_channel = None
    channel_owner = None
    
    # Check if target is a Discord channel link
    if isinstance(target, str) and "discord.com/channels/" in target:
        try:
            # Extract channel ID from Discord link
            # Format: https://discord.com/channels/guild_id/channel_id
            parts = target.split("/")
            if len(parts) >= 2:
                channel_id = int(parts[-1])  # Last part is channel ID
                owner_channel = ctx.guild.get_channel(channel_id)
                
                if not owner_channel:
                    embed = await create_response_embed("Invalid Channel", "Channel not found or not accessible")
                    await ctx.reply(embed=embed, mention_author=False)
                    return
                
                # Check if this channel has an owner
                if channel_id in channel_owners:
                    channel_owner = channel_owners[channel_id]
                else:
                    embed = await create_response_embed("Not Found", "This channel doesn't have an active owner")
                    await ctx.reply(embed=embed, mention_author=False)
                    return
            else:
                raise ValueError("Invalid link format")
                
        except (ValueError, IndexError):
            embed = await create_response_embed("Invalid Link", "Please provide a valid Discord channel link")
            await ctx.reply(embed=embed, mention_author=False)
            return
    
    # Check if target is a channel ID (numeric string)
    elif isinstance(target, str) and target.isdigit():
        try:
            channel_id = int(target)
            owner_channel = ctx.guild.get_channel(channel_id)
            
            if not owner_channel:
                embed = await create_response_embed("Invalid Channel", "Channel not found or not accessible")
                await ctx.reply(embed=embed, mention_author=False)
                return
            
            # Check if this channel has an owner
            if channel_id in channel_owners:
                channel_owner = channel_owners[channel_id]
            else:
                embed = await create_response_embed("Not Found", "This channel doesn't have an active owner")
                await ctx.reply(embed=embed, mention_author=False)
                return
                
        except ValueError:
            embed = await create_response_embed("Invalid Input", "Please provide a valid channel ID or mention a user")
            await ctx.reply(embed=embed, mention_author=False)
            return
    
    # If target is a Discord Member (mentioned user)
    elif isinstance(target, discord.Member):
        channel_owner = target
        # Find the channel owned by this member
        for vc_id, owner in channel_owners.items():
            if owner.id == target.id:
                owner_channel = ctx.guild.get_channel(vc_id)
                break
                
        if not owner_channel:
            embed = await create_response_embed("Not Found", f"{target.mention} doesn't own an active voice channel")
            await ctx.reply(embed=embed, mention_author=False)
            return
    
    else:
        # Try to convert string to member if it's not a digit
        try:
            converter = commands.MemberConverter()
            channel_owner = await converter.convert(ctx, str(target))
            
            # Find the channel owned by this member
            for vc_id, owner in channel_owners.items():
                if owner.id == channel_owner.id:
                    owner_channel = ctx.guild.get_channel(vc_id)
                    break
                    
            if not owner_channel:
                embed = await create_response_embed("Not Found", f"{channel_owner.mention} doesn't own an active voice channel")
                await ctx.reply(embed=embed, mention_author=False)
                return
                
        except commands.BadArgument:
            embed = await create_response_embed("Invalid Input", "Please mention a valid user, provide a valid channel ID, or paste a channel link")
            await ctx.reply(embed=embed, mention_author=False)
            return
       
    if ctx.author in owner_channel.members:
        embed = await create_response_embed("Already Inside", "You're already in the voice channel")
        await ctx.reply(embed=embed, mention_author=False)
        return
       
    class RequestAccessView(discord.ui.View):
        def __init__(self, requester):
            super().__init__(timeout=20)
            self.requester = requester
            self.message = None
            self.responded = False  # Track if a response has been given
            
        async def on_timeout(self):
            # Only show timeout message if no response was given
            if self.message and not self.responded:
                for item in self.children:
                    item.disabled = True
                try:
                    await self.message.edit(content="⏳ Request timed out. No response received.", view=self)
                except (discord.NotFound, discord.HTTPException):
                    # Message or channel was deleted, nothing we can do
                    pass
                
        @discord.ui.button(label="✅ Accept", style=discord.ButtonStyle.success)
        async def accept(self, interaction: discord.Interaction, button: discord.ui.Button):
            if interaction.user.id != channel_owner.id:
                try:
                    await interaction.response.send_message("Only the owner can accept requests", ephemeral=True)
                except (discord.NotFound, discord.HTTPException):
                    pass  # Interaction/channel no longer exists
                return
            
            # Mark as responded to prevent timeout override
            self.responded = True
            
            try:
                await owner_channel.set_permissions(self.requester, connect=True)
                await interaction.response.edit_message(content=f"{self.requester.mention} has been granted `Connect perm.` ✅", view=None)
                await ctx.send(f"{self.requester.mention}, you've been granted `Connect perm.` to {owner_channel.name}", delete_after=20)
            except (discord.NotFound, discord.HTTPException):
                # Channel/message was deleted or interaction is invalid - try to notify user if possible
                try:
                    if not interaction.response.is_done():
                        await interaction.response.send_message("❌ Channel no longer exists or an error occurred", ephemeral=True)
                    else:
                        await interaction.followup.send("❌ Channel no longer exists or an error occurred", ephemeral=True)
                except (discord.NotFound, discord.HTTPException):
                    # Even the interaction is completely invalid, nothing we can do
                    pass
            
        @discord.ui.button(label="❌ Reject", style=discord.ButtonStyle.danger)
        async def reject(self, interaction: discord.Interaction, button: discord.ui.Button):
            if interaction.user.id != channel_owner.id:
                try:
                    await interaction.response.send_message("Only the owner can reject requests", ephemeral=True)
                except (discord.NotFound, discord.HTTPException):
                    pass  # Interaction/channel no longer exists
                return
            
            # Mark as responded to prevent timeout override
            self.responded = True
            
            try:
                await owner_channel.set_permissions(self.requester, connect=False)
                await interaction.response.edit_message(content=f"{self.requester.mention} was rejected ❌", view=None)
                await ctx.send(f"{self.requester.mention}, your request to join {owner_channel.name} was denied", delete_after=15)
            except (discord.NotFound, discord.HTTPException):
                # Channel/message was deleted or interaction is invalid - try to notify user if possible
                try:
                    if not interaction.response.is_done():
                        await interaction.response.send_message("❌ Channel no longer exists or an error occurred", ephemeral=True)
                    else:
                        await interaction.followup.send("❌ Channel no longer exists or an error. occurred", ephemeral=True)
                except (discord.NotFound, discord.HTTPException):
                    # Even the interaction is completely invalid, nothing we can do
                    pass
    
    view = RequestAccessView(ctx.author)
    embed = await create_response_embed("Voice Join Request", f"{ctx.author.mention} is requesting to join `{owner_channel.name}`")
    sent_message = await owner_channel.send(content=channel_owner.mention, embed=embed, view=view)
    view.message = sent_message  # Store the sent message in the view
    await ctx.reply(f"✅ Request sent to {channel_owner.mention} ➜", mention_author=False)


@bot.command(name='lock')
@commands.check(is_in_target_category)
async def lock(ctx):
    if not await is_channel_owner_or_manager(ctx):
        return
     # Check cooldown before executing command
    if await check_cooldown(ctx):
        return

    channel = ctx.author.voice.channel
     member_role = discord.utils.get(ctx.guild.roles, name="┆Member")

    if member_role:
        await channel.set_permissions(member_role, connect=False)
        for member in channel.members:
            await channel.set_permissions(member, connect=True)
        
        # Add lock emoji to the channel name
        if not channel.name.startswith("🔒"):
            await channel.edit(name=f"🔒 {channel.name}")
        
        embed = await create_response_embed("Channel Locked", f"{ctx.author.mention} **Successfully `locked` the channel** 🔒")
    else:
        embed = await create_response_embed("Error", "The @Member role was not found.")

    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='unlock')
@commands.check(is_in_target_category)
async def unlock(ctx):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    
    channel = ctx.author.voice.channel
     member_role = discord.utils.get(ctx.guild.roles, name="┆Member")
    
    if member_role:
        await channel.set_permissions(member_role, connect=True)
        
        # Remove lock emoji from the channel name
        if channel.name.startswith("🔒"):
            await channel.edit(name=channel.name[2:].strip())
        
        embed = await create_response_embed("Channel Unlocked", f"{ctx.author.mention} **`unlocked` the channel** 🔓") 
    else:
        embed = await create_response_embed("Error", "The @Member role was not found.")
    
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='transfer')
@commands.check(is_in_target_category)
async def transfer(ctx, member: discord.Member):
    # Only the owner can transfer ownership
    if not await is_channel_owner(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
   
    if not ctx.author.voice or not ctx.author.voice.channel:
        return
   
    channel = ctx.author.voice.channel
    if member.bot:
        embed = await create_response_embed("Error", "You cannot transfer ownership to a bot!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    # Check if the member is in the same voice channel
    if not member.voice or member.voice.channel != channel:
        embed = await create_response_embed("Error", "You can only transfer ownership to users in the same voice channel!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    # Create confirmation view
    class TransferConfirmView(discord.ui.View):
        def __init__(self):
            super().__init__(timeout=30.0)
            self.confirmed = None
        
        @discord.ui.button(label='Yes', style=discord.ButtonStyle.green, emoji='✅')
        async def confirm_yes(self, interaction: discord.Interaction, button: discord.ui.Button):
            if interaction.user != ctx.author:
                await interaction.response.send_message("Only the channel owner can confirm this action!", ephemeral=True)
                return
            
            self.confirmed = True
            self.stop()
            
            # Transfer ownership
            channel_owners[channel.id] = member
            embed = await create_response_embed(
                "Ownership Transfer",
                f"👑 {ctx.author.mention} **transefered ownership to** {member.mention}!"
            )
            await interaction.response.edit_message(embed=embed, view=None)
        
        @discord.ui.button(label='No', style=discord.ButtonStyle.red, emoji='❌')
        async def confirm_no(self, interaction: discord.Interaction, button: discord.ui.Button):
            if interaction.user != ctx.author:
                await interaction.response.send_message("Only the channel owner can confirm this action!", ephemeral=True)
                return
            
            self.confirmed = False
            self.stop()
            
            embed = await create_response_embed("Transfer Cancelled", "Ownership transfer has been cancelled.")
            await interaction.response.edit_message(embed=embed, view=None)
        
        async def on_timeout(self):
            embed = await create_response_embed("Transfer Timeout", "Ownership transfer has timed out.")
            try:
                await self.message.edit(embed=embed, view=None)
            except:
                pass
    
    # Create confirmation embed and view
    view = TransferConfirmView()
    embed = await create_response_embed(
        "Confirm Ownership Transfer",
        f"Transfer ownership to {member.mention}?\n\nThis action cannot be undone."
    )
    message = await ctx.reply(embed=embed, view=view, mention_author=False)
    view.message = message

@bot.command(name='limit')
@commands.check(is_in_target_category)
async def limit(ctx, limit: int):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    
    channel = ctx.author.voice.channel
    if limit < 0:
        embed = await create_response_embed("⚠️ Error", "Member limit must be 0 or higher!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    await channel.edit(user_limit=limit)
    embed = await create_response_embed("Channel Limit Updated", f"{ctx.author.mention} Set channel limit to `{limit}`! ")
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='claim')
@commands.check(is_in_target_category)
async def claim(ctx):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    if not ctx.author.voice or not ctx.author.voice.channel:
        embed = await create_response_embed("Error", "You need to be in a voice channel to claim it!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    channel = ctx.author.voice.channel
    if channel.id not in bot_created_channels:
        embed = await create_response_embed("Error", "This command only works in channels created by the bot!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    owner = channel_owners.get(channel.id)
    if owner and owner in channel.members:
        embed = await create_response_embed(
            "Claim Refused",
            f"👑 {owner.mention} **is the current channel owner**!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    channel_owners[channel.id] = ctx.author
    embed = await create_response_embed(
        "Channel Claimed",
        f"👑 {ctx.author.mention} **Succesfully claimed the channel**!"
    )
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='vcinfo')
@commands.check(is_in_target_category)
async def info(ctx):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    if not ctx.author.voice or not ctx.author.voice.channel:
        embed = await create_response_embed("Error", "You need to be in a voice channel to use this command!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    channel = ctx.author.voice.channel
    if channel.id not in bot_created_channels:
        embed = await create_response_embed("Error", "Command works only in a `BɅD ZONE | Tempy` VC!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    members_in_voice = '\n'.join([member.mention for member in channel.members]) or "None"
    permitted_members = '\n'.join([member.mention for member, perms in channel.overwrites.items() if isinstance(member, discord.Member) and perms.connect]) or "None"
    rejected_members = '\n'.join([member.mention for member, perms in channel.overwrites.items() if isinstance(member, discord.Member) and perms.connect is False]) or "None"
    
    # Text muted users (members with send_messages explicitly denied)
    text_muted_members = []
    for member, perms in channel.overwrites.items():
        if isinstance(member, discord.Member) and hasattr(perms, 'send_messages') and perms.send_messages is False:
            text_muted_members.append(member.mention)
    text_muted_members = '\n'.join(text_muted_members) or "None"
    
    owner = channel_owners.get(channel.id, "Unknown")
   
    # Retrieve channel managers
    managers = channel_managers.get(channel.id, set())
    managers_list = '\n'.join([manager.mention for manager in managers]) or "None"
   
    # Check if the channel is locked
     member_role = discord.utils.get(ctx.guild.roles, name="┆Member")
    is_locked = False
    if member_role:
        overwrites = channel.overwrites_for(member_role)
        if overwrites.connect is False:
            is_locked = True
   
    embed = discord.Embed(
        title=f"{channel.name} - `Voice Info`",
        color=0x2b2d31
    )
    if bot.user.avatar:
        embed.set_thumbnail(url=bot.user.avatar.url)
    else:
        embed.set_thumbnail(url=bot.user.default_avatar.url)
   
    embed.add_field(name="`Owner`", value=f"{owner.mention if isinstance(owner, discord.Member) else owner}", inline=False)
    embed.add_field(name="`Channel Status`", value="Locked 🔒" if is_locked else "Open", inline=True)
    embed.add_field(name="`Created`", value=f"{discord.utils.format_dt(channel.created_at, 'R')}", inline=True)
    embed.add_field(name="`Channel Limit`", value=str(channel.user_limit), inline=True)
    embed.add_field(name="`In Voice`", value=members_in_voice, inline=False)
    embed.add_field(name="`Managers`", value=managers_list, inline=False)
    embed.add_field(name=" `Permitted Users`", value=permitted_members, inline=False)
    embed.add_field(name="`Rejected Users`", value=rejected_members, inline=False)
    embed.add_field(name="`Text Muted Users`", value=text_muted_members, inline=False)
   
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='reject')
@commands.check(is_in_target_category)
async def reject(ctx, members: commands.Greedy[discord.Member]):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
   
    if not members:
        await ctx.reply("Please mention at least one user to reject.", mention_author=False)
        return
   
    channel = ctx.author.voice.channel
    rejected_members = []
   
    for member in members:
        # Check if user is trying to target themselves
        if member == ctx.author:
            embed = await create_response_embed(
                "Invalid Target",
                "❌ You cannot reject yourself!"
            )
            await ctx.reply(embed=embed, mention_author=False)
            continue
            
        # Check if trying to reject the owner
        if not await check_not_targeting_owner(ctx, member):
            continue
       
        overwrites = channel.overwrites_for(member)
        overwrites.connect = False
        await channel.set_permissions(member, overwrite=overwrites)
       
        if member in channel.members:
            try:
                await member.move_to(None)
            except discord.HTTPException:
                pass
       
        rejected_members.append(member)
   
    if rejected_members:
        mentions = ', '.join(member.mention for member in rejected_members)
        embed = await create_response_embed(
            "Users Rejected ❌",
            f"{ctx.author.mention} **Removed `Connect perm.` from** {mentions}!"
        )
        await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='owner')
@commands.check(is_in_target_category)
async def owner(ctx):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    if not ctx.author.voice or not ctx.author.voice.channel:
        embed = await create_response_embed("Error", "You need to be in a voice channel!")
        await ctx.reply(embed=embed, mention_author=False)
        return

    channel = ctx.author.voice.channel
    if channel.id not in bot_created_channels:
        embed = await create_response_embed("Error", "Command works only in a `BɅD ZONE | Tempy` VC!")
        await ctx.reply(embed=embed, mention_author=False)
        return

    owner = channel_owners.get(channel.id)
    if owner:
        embed = await create_response_embed(
            "Channel Owner",
            f"**The current channel owner is** {owner.mention}"
        )
    else:
        embed = await create_response_embed(
            "Channel Owner",
            "⚠️ This VC has no active owner! Use .v claim to claim ownership."
        )
    await ctx.reply(embed=embed, mention_author=False)

async def check_rename_limit(ctx):
    """Check if user has exceeded rename limit"""
    user_id = ctx.author.id
    current_time = time.time()
    user_data = rename_usage[user_id]
    
    # Reset count if cooldown period has passed
    if current_time >= user_data['reset_time']:
        user_data['count'] = 0
        user_data['reset_time'] = current_time + COOLDOWN_DURATION
    
    # Check if user has reached the limit
    if user_data['count'] >= MAX_RENAMES:
        time_left = int(user_data['reset_time'] - current_time)
        minutes = time_left // 60
        seconds = time_left % 60
        
        embed = discord.Embed(
            title="❌ Rename Limit Reached",
            description=f"You have used all {MAX_RENAMES} renames. Try again in {minutes}m {seconds}s.",
            color=0xff0000
        )
        await ctx.reply(embed=embed, mention_author=False)
        return True
    
    return False

async def update_rename_count(ctx):
    """Update the rename count for the user"""
    user_id = ctx.author.id
    rename_usage[user_id]['count'] += 1
    
    # Calculate remaining renames
    remaining = MAX_RENAMES - rename_usage[user_id]['count']
    time_left = int(rename_usage[user_id]['reset_time'] - time.time())
    minutes = time_left // 60
    
    return remaining, minutes

@bot.command(name='rename')
@commands.check(is_in_target_category)
async def name(ctx, *, new_name: str):
    if not await is_channel_owner_or_manager(ctx):
        return
    
    # Check existing cooldown (if you have other cooldowns)
    if await check_cooldown(ctx):
        return
    
    # Check rename limit
    if await check_rename_limit(ctx):
        return
   
    channel = ctx.author.voice.channel
    await channel.edit(name=new_name)
    
    # Update rename count and get remaining info
    remaining, reset_minutes = await update_rename_count(ctx)
    
    # Create success embed with remaining renames info
    embed = await create_response_embed(
        "✅ Channel Renamed",
        f"**Channel renamed to** `{new_name}`!\n"
        f"You have `{remaining} rename(s) left` **(resets in {reset_minutes} minutes)**."
    )
    await ctx.reply(embed=embed, mention_author=False)

# Optional: Command to check remaining renames
@bot.command(name='renames')
@commands.check(is_in_target_category)
async def check_renames(ctx):
    """Check how many renames are left"""
    user_id = ctx.author.id
    current_time = time.time()
    user_data = rename_usage[user_id]
    
    # Reset count if cooldown period has passed
    if current_time >= user_data['reset_time']:
        user_data['count'] = 0
        user_data['reset_time'] = current_time + COOLDOWN_DURATION
    
    remaining = MAX_RENAMES - user_data['count']
    time_left = int(user_data['reset_time'] - current_time)
    minutes = time_left // 60
    seconds = time_left % 60
    
    embed = discord.Embed(
        title="🔄 Rename Status",
        description=f"Renames remaining: **{remaining}/{MAX_RENAMES}**\n"
                   f"Resets in: {minutes}m {seconds}s",
        color=0x00ff00 if remaining > 0 else 0xff0000
    )
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='camon')
@commands.check(is_in_target_category)
async def camon(ctx, members: commands.Greedy[discord.Member]):
    if not await is_channel_owner_or_manager(ctx):
        return
    
    if not members:
        await ctx.reply("Please mention at least one user to enable camera for.", mention_author=False)
        return
   
    # Remove self from the list of members
    members = [member for member in members if member != ctx.author]
   
    if not members:
        await ctx.reply("You cannot modify your own camera permissions.", mention_author=False)
        return
   
    channel = ctx.author.voice.channel
    for member in members:
        await channel.set_permissions(member, stream=True)
   
    mentions = ', '.join(member.mention for member in members)
    embed = await create_response_embed(
        "Cam/Stream Enabled",
        f"{ctx.author.mention} **Granted `Video perm.` for** {mentions}. 📹"
    )
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='camoff')
@commands.check(is_in_target_category)
async def camoff(ctx, members: commands.Greedy[discord.Member]):
    if not await is_channel_owner_or_manager(ctx):
        return
   
    if not members:
        await ctx.reply("Please mention at least one user to disable camera for.", mention_author=False)
        return
   
    # Remove self from the list of members
    members = [member for member in members if member != ctx.author]
   
    if not members:
        await ctx.reply("You cannot modify your own camera permissions.", mention_author=False)
        return
   
    channel = ctx.author.voice.channel
    disconnected_users = []
    
    for member in members:
        # Remove stream permission first
        await channel.set_permissions(member, stream=False)
        
        # If member is currently in the voice channel and streaming, disconnect them
        if member.voice and member.voice.channel == channel:
            if member.voice.self_stream or member.voice.self_video:
                await member.move_to(None)  # Disconnect from voice
                disconnected_users.append(member)
   
    mentions = ', '.join(member.mention for member in members)
    
    description = f"{ctx.author.mention} **Removed `Video perm.` for** {mentions}. 🚫"
    
    if disconnected_users:
        disconnected_mentions = ', '.join(member.mention for member in disconnected_users)
        description += f"\n\n🔌 **Disconnected users with cam/stream ON:** {disconnected_mentions}"
    
    embed = await create_response_embed(
        "Cam/Stream Disabled",
        description
    )
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name="status")
@commands.check(is_in_target_category)
async def update_voice_status(ctx, *, status_text: str):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    """Set custom voice channel status (push to endpoint)."""

    # Check if user is in a voice channel
    if not ctx.author.voice or not ctx.author.voice.channel:
        embed = await create_response_embed(
            "❌ Not in Voice Channel",
            "You must be in a voice channel to use this command."
        )
        await ctx.reply(embed=embed, mention_author=False)
        return

    channel = ctx.author.voice.channel

    # Check if the voice channel was created by the bot
    if channel.id not in bot_created_channels:
        embed = await create_response_embed(
            "❌ Error",
            "Command works only in a `BɅD ZONE | Tempy` VC."
        )
        await ctx.reply(embed=embed, mention_author=False)
        return

    # Proceed with status update
    url = f"https://discord.com/api/v10/channels/{channel.id}/voice-status"
    headers = {
        "Authorization": f"Bot {TOKEN}",
        "Content-Type": "application/json"
    }
    payload = {"status": status_text}

    res = requests.put(url, headers=headers, json=payload)

    if res.status_code in [200, 204]:
        embed = await create_response_embed(
            "Status successfully set",
            f"🗪 {ctx.author.mention} **Set status to** **`{status_text}`**"
        )
    else:
        embed = await create_response_embed(
            "❌ Failed to update",
            f"Status Code: {res.status_code}\nResponse: {res.text}"
        )

    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='perm')
@commands.check(is_in_target_category)
async def permit(ctx, members: commands.Greedy[discord.Member]):
    if not await is_channel_owner_or_manager(ctx):
        return

    if not members:
        await ctx.reply("Please mention at least one user to permit.", mention_author=False)
        return

    channel = ctx.author.voice.channel
    for member in members:
        await channel.set_permissions(member, connect=True)

    mentions = ', '.join(member.mention for member in members)
    embed = await create_response_embed(
        "Access Granted",
        f"{ctx.author.mention} **Granted `Connect perm.` to** {mentions} ! ✅"
    )
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='tmute')
@commands.check(is_in_target_category)
async def text_mute(ctx, members: commands.Greedy[discord.Member]):
    if not await is_channel_owner_or_manager(ctx):
        return
    if not members:
        await ctx.reply("Please mention at least one user to text mute.", mention_author=False)
        return
    
    # Remove self from the list of members to mute
    members = [member for member in members if member != ctx.author]
    
    if not members:
        await ctx.reply("You cannot text mute yourself.", mention_author=False)
        return
    
    channel = ctx.author.voice.channel
    for member in members:
        await channel.set_permissions(member, send_messages=False)
    mentions = ', '.join(member.mention for member in members)
    embed = await create_response_embed(
        "Text Muted",
        f"{ctx.author.mention} **Removed `Send messages perm.` for** {mentions}! ⚠️"
    )
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='tunmute')
@commands.check(is_in_target_category)
async def text_unmute(ctx, members: commands.Greedy[discord.Member]):
    if not await is_channel_owner_or_manager(ctx):
        return
    if not members:
        await ctx.reply("Please mention at least one user to text unmute.", mention_author=False)
        return
    channel = ctx.author.voice.channel
    for member in members:
        await channel.set_permissions(member, send_messages=None)  # Reset to default
    mentions = ', '.join(member.mention for member in members)
    embed = await create_response_embed(
        "Text Unmuted",
        f"{ctx.author.mention} **Granted `Send messages perm` to** {mentions}.` ! ⚠️"
    )
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='sboff')
@commands.check(is_in_target_category)
async def sboff(ctx):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return

    channel = ctx.author.voice.channel
    for member in channel.members:
        # Don't disable soundboard for the owner if a manager is using the command
        if member == channel_owners.get(channel.id) and ctx.author != member:
            continue
        await channel.set_permissions(member, use_soundboard=False)

    embed = await create_response_embed(
        "Soundboard Disabled",
        "The soundboard is disabled .🔇"
    )
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='sbon')
@commands.check(is_in_target_category)
async def sbon(ctx):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return

    channel = ctx.author.voice.channel
    for member in channel.members:
        await channel.set_permissions(member, use_soundboard=True)

    embed = await create_response_embed(
        "Soundboard Enabled",
        "The soundboard is enabled . 🔊"
    )
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='chatdlt')
@commands.check(is_in_target_category)
async def chat_dlt(ctx, amount: int = None):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    
    # Use existing permission check function
    if not await is_channel_owner_or_manager(ctx):
        return
    
    # Check if amount is specified and valid
    if amount is None or amount <= 0:
        embed = await create_response_embed("Error", "Please specify a valid number of messages to delete (e.g., `.v chatdlt 5`).")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    # Limit to reasonable amount to prevent abuse
    if amount > 100:
        embed = await create_response_embed("Error", "You can only delete up to 100 messages at once.")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    try:
        # Delete messages excluding the command message
        deleted = await ctx.channel.purge(limit=amount, check=lambda msg: msg.id != ctx.message.id)
        
        # Send confirmation as a reply to the command (and keep it visible)
        embed = await create_response_embed("Messages Deleted", f"{ctx.author.mention} **Successfully deleted {len(deleted)} messages**.")
        await ctx.reply(embed=embed, mention_author=False)
        
    except discord.Forbidden:
        embed = await create_response_embed("Error", "I don't have permission to delete messages in this channel.")
        await ctx.reply(embed=embed, mention_author=False)
        
    except discord.HTTPException as e:
        embed = await create_response_embed("Error", f"An error occurred while deleting messages: {str(e)}")
        await ctx.reply(embed=embed, mention_author=False)
      
@bot.command(name='cmd')
@commands.check(is_in_target_category)
async def cmd(ctx):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    
    embed = await create_response_embed(
        "All Commands Here",
        "**<:deathzone:1250199513550028956>  Use .v as prefix!**\n\n"
        "**⚠️ Channel Controls**\n"
        "`lock` - Locks channel\n"
        "`unlock` - Unlocks channel\n"
        "`vcinfo` - Channel info\n"
        "`limit` - Set limit\n"
        "`rename` - Renames channel\n"
        "`status` - Set channel status\n\n"
        
        "**<:GFX:1338041230998638603> User Management**\n"
        "`perm` - Permit User\n"
        "`reject` - Reject User\n"
        "`tmute` - Mute text.\n"
        "`tunmute` - Unmutes text.\n"
        "`camoff` - Disable Cam/Stream.\n"
        "`camon` - Enable Cam/Stream.\n"
        "`man add` - Adds man.\n"
        "`man remove` - Removes Man.\n"
        "`manager` - Show managers list.\n\n"
        
        "**<:OwnerCrown:1338041249025884191> Ownership**\n"
        "`transfer` - Transfer owner\n"
        "`claim` - Claim channel\n"
        "`owner` - Show owner\n\n"
        
        "**🔧 Misc**\n"
        "`request` - Request joining\n"
        "`renames` - Check renaming\n"
        "`chatdlt` - Clear chat\n"
        "`sbon` - Soundboard on\n"
        "`sboff` - Soundboard off\n\n"

        "** <:deathzone:1250199513550028956> Prime (Admin Only)**\n"
        "`cldcheck` - Show .v cooldowns.\n"
        "`cldclr` - Clear .v cooldown. \n\n"
    )
    
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='cldcheck')
async def check_cooldowns(ctx):
    """Check list of users on cooldown (admin only)"""
    # Check if user is admin (you can modify this check based on your admin system)
    if not ctx.author.guild_permissions.administrator:
        embed = await create_response_embed("Error", "This command is for administrators only!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    if not unknown_command_cooldowns:
        embed = await create_response_embed("Cooldown List", "No users are currently on cooldown.")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    current_time = time.time()
    cooldown_list = []
    
    for user_id, cooldown_end in unknown_command_cooldowns.items():
        if current_time < cooldown_end:
            user = bot.get_user(user_id)
            if user:
                time_left = int((cooldown_end - current_time) / 60)  # Convert to minutes
                cooldown_list.append(f"**{user.display_name}** ({user.mention}) - {time_left} minutes left")
            else:
                time_left = int((cooldown_end - current_time) / 60)
                cooldown_list.append(f"**Unknown User** (ID: {user_id}) - {time_left} minutes left")
    
    if not cooldown_list:
        embed = await create_response_embed("Cooldown List", "No users are currently on cooldown.")
    else:
        description = "\n".join(cooldown_list)
        embed = discord.Embed(
            title="<:time:1257454120739016764> Users on Cooldown",
            description=description,
            color=0xff6b6b
        )
        embed.set_footer(text=f"Total users on cooldown: {len(cooldown_list)}")
    
    await ctx.reply(embed=embed, mention_author=False)

@bot.command(name='cldclr')
async def clear_cooldown(ctx, user: discord.Member = None):
    """Clear cooldown for a specific user (admin only)"""
    # Check if user is admin (you can modify this check based on your admin system)
    if not ctx.author.guild_permissions.administrator:
        embed = await create_response_embed("Error", "This command is for administrators only!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    if not user:
        embed = await create_response_embed("Error", "Please mention a user to clear their cooldown!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    user_id = user.id
    
    # Check if user is on cooldown
    if user_id not in unknown_command_cooldowns:
        embed = await create_response_embed("Error", f"{user.mention} is not on cooldown!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    # Clear the cooldown
    if user_id in unknown_command_cooldowns:
        del unknown_command_cooldowns[user_id]
    if user_id in unknown_command_attempts:
        del unknown_command_attempts[user_id]
    
    embed = await create_response_embed(
        "Cooldown Cleared",
        f"✅ Cooldown has been cleared for {user.mention}!"
    )
    await ctx.reply(embed=embed, mention_author=False)
    
    # Log the action
    print(f"Admin {ctx.author} cleared cooldown for {user} ({user_id})")

# Store message IDs for each channel's log
channel_log_messages = {}  # Format: {channel_id: message_id}
@bot.event
async def on_guild_channel_update(before, after):
    # Only track updates to bot-created voice channels
    if after.id in bot_created_channels and isinstance(after, discord.VoiceChannel):
        # If name changed, update the log
        if before.name != after.name:
            await update_voice_channel_log(after, "rename", None)

        
            
            # Store message IDs for each channel's log

channel_log_messages = {}  # Format: {channel_id: message_id}
deleted_channel_pages = {}


# Store message IDs for each channel's log
channel_log_messages = {}  # Format: {channel_id: message_id}

# Store pagination data for each channel
channel_log_pages = {}  # Format: {channel_id: {"pages": [embed1, embed2...], "current_page": 0}}

class PaginationView(discord.ui.View):
    def __init__(self, channel_id):
        super().__init__(timeout=None)
        self.channel_id = channel_id

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        # only allow certain people to use buttons if needed
        return True

    @discord.ui.button(emoji="◀️", style=discord.ButtonStyle.grey)
    async def previous_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.change_page(interaction, -1)

    @discord.ui.button(emoji="▶️", style=discord.ButtonStyle.grey)
    async def next_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.change_page(interaction, 1)

    async def change_page(self, interaction: discord.Interaction, direction: int):
        # Try normal active channel pages first
        pagination_data = channel_log_pages.get(self.channel_id)

        # If channel is deleted, fallback to deleted_channel_pages
        if not pagination_data:
            pagination_data = deleted_channel_pages.get(self.channel_id)

        if not pagination_data:
            await interaction.response.send_message("No pagination data found.", ephemeral=True)
            return

        pages = pagination_data["pages"]
        current_page = pagination_data["current_page"]

        # calculate new page
        new_page = current_page + direction
        if new_page < 0:
            new_page = 0
        elif new_page >= len(pages):
            new_page = len(pages) - 1

        pagination_data["current_page"] = new_page
        await interaction.response.edit_message(embed=pages[new_page], view=self)

async def update_voice_channel_log(channel, event_type=None, user=None):
    """Properly update the log embed for a voice channel with working pagination"""
    log_channel_id = 1345865496230236322  # your log channel id
    log_channel = channel.guild.get_channel(log_channel_id)
    if not log_channel:
        return

    owner = channel_owners.get(channel.id)
    current_time = discord.utils.utcnow().strftime("%H:%M:%S")

    existing_message = None
    activity_log = []

    # Try to fetch previous activity if exists
    if channel.id in channel_log_pages:
        page_data = channel_log_pages[channel.id]
        # Collect all previous activity logs from all pages
        for embed in page_data["pages"]:
            for field in embed.fields:
                if "Activity Log" in field.name:
                    activity_log.extend(field.value.split('\n'))

    # Build the new event log entry
    new_entry = None
    if event_type:
        if event_type == "create":
            new_entry = f"`{current_time}` 🔨 Channel created by {user.mention}"
        elif event_type == "join":
            new_entry = f"`{current_time}` ➡️ {user.mention} joined the channel"
        elif event_type == "leave":
            new_entry = f"`{current_time}` ⬅️ {user.mention} left the channel"
        elif event_type == "rename":
            new_entry = f"`{current_time}` 🔄 Channel renamed to **{channel.name}**"
        elif event_type == "delete":
            new_entry = f"`{current_time}` 🗑️ Channel deleted"

    # Add the new event if it's not a duplicate
    if new_entry and (new_entry not in activity_log[:10]):  # Check recent 10
        activity_log.insert(0, new_entry)

    # Create pages again by splitting activity log
    entries_per_page = 8
    activity_log_chunks = [activity_log[i:i+entries_per_page] for i in range(0, len(activity_log), entries_per_page)]

    # Create the base embed
    if event_type == "delete":
        base_embed = discord.Embed(
            title=f"{channel.name} - DELETED",
            description="This voice channel has been deleted.",
            color=0xFF5555
        )
        if owner:
            base_embed.add_field(name="👑 Former Owner", value=f"{owner.mention}", inline=True)
            base_embed.set_thumbnail(url=owner.display_avatar.url)
        base_embed.set_footer(text=f"Deleted at: {current_time}")
    else:
        base_embed = discord.Embed(
            title=f"{channel.name} - Voice Info",
            color=0x36393F
        )
        if owner:
            base_embed.add_field(name="👑 Owner", value=f"{owner.mention}", inline=False)
        base_embed.add_field(name="⏳ Created", value=current_time, inline=True)
        base_embed.add_field(name="👥 Channel Limit", value=str(channel.user_limit or "0"), inline=True)

        managers = []
        if channel.id in channel_managers:
            managers = [channel.guild.get_member(uid) for uid in channel_managers[channel.id]]
            managers = [m for m in managers if m]
        managers_text = "\n".join([m.mention for m in managers]) or "None"

        if owner:
            base_embed.set_thumbnail(url=owner.display_avatar.url)

    pages = []

    # Attach first chunk of activity log to base page
    if activity_log_chunks:
        base_embed.add_field(
            name="📝 Activity Log",
            value="\n".join(activity_log_chunks[0]),
            inline=False
        )
    timestamp_info = f"Last updated: {current_time}"
    base_embed.set_footer(text=f"Page 1/{max(1, len(activity_log_chunks))} • {timestamp_info}")
    pages.append(base_embed)

    # Create additional pages if needed
    for i in range(1, len(activity_log_chunks)):
        extra_page = discord.Embed(
            title=f"{channel.name} - Activity Log",
            color=0x36393F
        )
        extra_page.add_field(
            name="📝 Activity Log (continued)",
            value="\n".join(activity_log_chunks[i]),
            inline=False
        )
        extra_page.set_footer(text=f"Page {i+1}/{len(activity_log_chunks)} • {timestamp_info}")
        pages.append(extra_page)

    # Save updated pagination
    channel_log_pages[channel.id] = {
        "pages": pages,
        "current_page": 0
    }

    pagination_view = PaginationView(channel.id)

    # Edit or send the message
    if channel.id in channel_log_messages:
        try:
            msg_id = channel_log_messages[channel.id]
            existing_message = await log_channel.fetch_message(msg_id)
            await existing_message.edit(embed=pages[0], view=pagination_view)
            return existing_message
        except (discord.NotFound, discord.HTTPException):
            pass

    # If no message exists, send a new one
    message = await log_channel.send(embed=pages[0], view=pagination_view)
    channel_log_messages[channel.id] = message.id
    return message

@bot.event
async def on_voice_state_update(member, before, after):
    # Handle channel creation
    if after.channel and after.channel.name == "➕・Make Your Room":
        category = after.channel.category
        if category:
            # Create the voice channel
            vc = await category.create_voice_channel(
                f"{member.display_name}",
                reason="Voice channel for user interaction"
            )
           
            # Set video quality to full
            await vc.edit(video_quality_mode=discord.VideoQualityMode.full)
           
            # Set base permissions
            overwrites = discord.PermissionOverwrite()
            overwrites.connect = True
            overwrites.manage_channels = False
            overwrites.manage_permissions = False
            await vc.set_permissions(member, overwrite=overwrites)
           
            # Register channel and owner
            channel_owners[vc.id] = member
            bot_created_channels.add(vc.id)

            # Move member to new channel
            await member.move_to(vc)
           
            # Create voice control panel
            control_panel = VoiceControlPanel(vc.id)

            # 👇 Set default voice status here
            url = f"https://discord.com/api/v10/channels/{vc.id}/voice-status"
            headers = {
                "Authorization": f"Bot {TOKEN}",  # make sure TOKEN is defined
                "Content-Type": "application/json"
            }
            payload = {
                "status": "**<:deathzone:1250199513550028956> .v status to customize <:deathzone:1250199513550028956>**"
            }
            try:
                requests.put(url, headers=headers, json=payload)
            except Exception as e:
                print(f"Failed to set default status: {e}")
           
            # Check if user is a founder and create appropriate welcome message
            if member.id == FOUNDER_ID:
                # Founder welcome message
                embed = await create_response_embed(
                    "Tempy voice manager!",
                    f"**Yo `{member.display_name}` As a Founder of** `{SERVER_NAME}`, **you have unlimited usage**\n"
                    "**<:deathzone:1250199513550028956> Use `.v` as prefix !**\n"
                )
            else:
                # Regular user welcome message
                embed = await create_response_embed(
                    "Tempy voice manager!",
                    f"**Yo `{member.display_name}` Manage your voice channel with me.**\n"
                    "**<:deathzone:1250199513550028956>  Use `.v` as prefix !**\n"
                )
            
            embed.set_footer(text=f"Created for {member.name}", icon_url=member.display_avatar.url)
           
            # Send the message with both embed and view, Using the proper silent parameter 
            await vc.send(content=member.mention, embed=embed, view=control_panel, silent=True)


    # Handle member joining a voice channel - SINGLE MERGED HANDLER
    if after.channel and after.channel.id in bot_created_channels and not before.channel == after.channel:
        # If no log exists yet, create first log (for newly created or first joined channels)
        if after.channel.id not in channel_log_messages:
            await update_voice_channel_log(after.channel, "create", member)
        else:
            await update_voice_channel_log(after.channel, "join", member)
    
    # Handle member leaving a voice channel
    if before.channel and before.channel.id in bot_created_channels and not before.channel == after.channel:
        # Check if channel will be deleted (empty)
        if len(before.channel.members) == 0:
            # Update the existing log to mark the channel as deleted
            # This modifies the existing log instead of creating a new embed
            await update_voice_channel_log(before.channel, "delete", member)
            
            # Cleanup tracking dictionaries
            if before.channel.id in channel_log_pages:
                # We'll keep the data in deleted_channel_pages for reference
                # assuming update_voice_channel_log handles this transfer when event_type is "delete"
                pass
            
            if before.channel.id in channel_owners:
                del channel_owners[before.channel.id]
            if before.channel.id in channel_managers:
                del channel_managers[before.channel.id]
            
            bot_created_channels.remove(before.channel.id)
            
            # Delete the channel
            await before.channel.delete()
        else:
            # Channel still has members, just log the leave
            await update_voice_channel_log(before.channel, "leave", member)

@bot.event
async def on_ready():
    print(f"Logged in as {bot.user}")
    print("Bot is ready!")


bot.run("MTI5ODY0OTQzNjQxMDYxMzg5MQ.GgY02q.fcusPxEfeimbjJrTT0pn1BTIWYsIAH7T6b_O6A")

