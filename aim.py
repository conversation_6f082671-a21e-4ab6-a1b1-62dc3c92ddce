import pyautogui
import cv2
import numpy as np
import time
import random
import threading
import win32api
import win32con
import win32gui
import mss
# Additional imports for advanced features (if needed later)
# from ctypes import windll, c_int, c_uint, c_void_p, c_long, byref
# from ctypes.wintypes import DWOR<PERSON>, BOOL, HANDLE, HWND

# Disable pyautogui failsafe for smoother operation
pyautogui.FAILSAFE = False

class UndetectedAutoFire:
    def __init__(self):
        # Target color #d3f150 in RGB
        self.target_rgb = (211, 241, 80)

        # Convert to HSV for better detection
        rgb_normalized = np.uint8([[[self.target_rgb[2], self.target_rgb[1], self.target_rgb[0]]]])
        hsv_color = cv2.cvtColor(rgb_normalized, cv2.COLOR_BGR2HSV)

        # Create HSV range with tolerance for better detection
        tolerance = 15
        self.lower_hsv = np.array([
            max(0, hsv_color[0][0][0] - tolerance),
            max(0, hsv_color[0][0][1] - 50),
            max(0, hsv_color[0][0][2] - 50)
        ])
        self.upper_hsv = np.array([
            min(179, hsv_color[0][0][0] + tolerance),
            255,
            255
        ])

        # Anti-detection settings
        self.detection_threshold = 100  # Minimum pixels to trigger
        self.fire_delay_min = 0.01  # Minimum delay between shots
        self.fire_delay_max = 0.03  # Maximum delay between shots
        self.scan_delay = 0.001  # Delay between scans

        # Screen capture optimization
        self.monitor = {"top": 0, "left": 0, "width": 1920, "height": 1080}
        self.sct = mss.mss()

        # State tracking
        self.is_running = False
        self.last_fire_time = 0

        # Get Valorant window handle
        self.valorant_hwnd = None
        self.find_valorant_window()

    def find_valorant_window(self):
        """Find Valorant window handle"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if "VALORANT" in window_text.upper():
                    windows.append(hwnd)
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        if windows:
            self.valorant_hwnd = windows[0]
            print(f"Found Valorant window: {win32gui.GetWindowText(self.valorant_hwnd)}")
        else:
            print("Valorant window not found - script will work on entire screen")

    def is_valorant_active(self):
        """Check if Valorant is the active window"""
        if not self.valorant_hwnd:
            return True  # If we can't find Valorant, assume it's active

        try:
            active_window = win32gui.GetForegroundWindow()
            return active_window == self.valorant_hwnd
        except:
            return True

    def human_like_click(self):
        """Simulate human-like mouse click with randomization"""
        current_time = time.time()

        # Add random delay to avoid pattern detection
        if current_time - self.last_fire_time < random.uniform(self.fire_delay_min, self.fire_delay_max):
            return

        # Use low-level mouse input for better stealth
        try:
            # Random micro-movements before click
            if random.random() < 0.3:  # 30% chance of micro-movement
                current_pos = win32gui.GetCursorPos()
                micro_x = random.randint(-2, 2)
                micro_y = random.randint(-2, 2)
                win32api.SetCursorPos((current_pos[0] + micro_x, current_pos[1] + micro_y))
                time.sleep(random.uniform(0.001, 0.003))

            # Perform click with random hold duration
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            time.sleep(random.uniform(0.01, 0.03))  # Human-like click duration
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)

            self.last_fire_time = current_time

        except Exception as e:
            print(f"Click error: {e}")

    def capture_screen_optimized(self):
        """Optimized screen capture using mss"""
        try:
            # Capture only the center area where enemies are likely to appear
            center_region = {
                "top": self.monitor["height"] // 4,
                "left": self.monitor["width"] // 4,
                "width": self.monitor["width"] // 2,
                "height": self.monitor["height"] // 2
            }

            screenshot = self.sct.grab(center_region)
            frame = np.array(screenshot)
            return cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
        except:
            # Fallback to pyautogui if mss fails
            screenshot = pyautogui.screenshot()
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

    def detect_target_color(self, frame):
        """Detect the target color #d3f150 in the frame"""
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

        # Create mask for target color
        mask = cv2.inRange(hsv, self.lower_hsv, self.upper_hsv)

        # Apply morphological operations to reduce noise
        kernel = np.ones((3, 3), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)

        # Count detected pixels
        detected_pixels = cv2.countNonZero(mask)

        return detected_pixels >= self.detection_threshold, detected_pixels

    def run_detection_loop(self):
        """Main detection loop with anti-detection measures"""
        print("Starting undetected auto-fire...")
        print(f"Target color: #{self.target_rgb[0]:02x}{self.target_rgb[1]:02x}{self.target_rgb[2]:02x}")
        print("Press Ctrl+C to stop")

        consecutive_detections = 0
        max_consecutive = 5  # Prevent spam clicking

        try:
            while self.is_running:
                # Only run when Valorant is active
                if not self.is_valorant_active():
                    time.sleep(0.1)
                    continue

                # Capture screen
                frame = self.capture_screen_optimized()

                # Detect target color
                target_detected, pixel_count = self.detect_target_color(frame)

                if target_detected:
                    consecutive_detections += 1

                    # Fire only if we haven't exceeded consecutive limit
                    if consecutive_detections <= max_consecutive:
                        self.human_like_click()
                        print(f"🎯 Target detected! Pixels: {pixel_count} | Shots: {consecutive_detections}")

                    # Add slight delay after detection to avoid spam
                    time.sleep(random.uniform(0.005, 0.015))
                else:
                    consecutive_detections = 0

                # Random scan delay for anti-detection
                time.sleep(self.scan_delay + random.uniform(0, 0.002))

        except KeyboardInterrupt:
            print("\n🛑 Auto-fire stopped by user")
        except Exception as e:
            print(f"❌ Error in detection loop: {e}")

    def start(self):
        """Start the auto-fire system"""
        if self.is_running:
            print("Auto-fire is already running!")
            return

        self.is_running = True
        detection_thread = threading.Thread(target=self.run_detection_loop, daemon=True)
        detection_thread.start()

        try:
            detection_thread.join()
        except KeyboardInterrupt:
            self.stop()

    def stop(self):
        """Stop the auto-fire system"""
        self.is_running = False
        print("🛑 Auto-fire stopped")

# Usage
if __name__ == "__main__":
    auto_fire = UndetectedAutoFire()

    print("🎮 Undetected Valorant Auto-Fire")
    print("=" * 40)
    print("Target Color: #d3f150 (Valorant enemy highlight)")
    print("Features:")
    print("- Human-like clicking patterns")
    print("- Anti-detection randomization")
    print("- Optimized screen capture")
    print("- Valorant window detection")
    print("=" * 40)

    input("Press Enter to start auto-fire...")
    auto_fire.start()
