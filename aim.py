import pyautogui
import cv2
import numpy as np
import time
import random
import threading
import mss
import ctypes
from ctypes import wintypes, windll
import keyboard
import mouse
import psutil
import os

# Disable pyautogui failsafe for smoother operation
pyautogui.FAILSAFE = False

class UndetectedAutoFire:
    def __init__(self):
        # Target color #d3f150 in RGB
        self.target_rgb = (211, 241, 80)

        # Convert to HSV for better detection
        rgb_normalized = np.uint8([[[self.target_rgb[2], self.target_rgb[1], self.target_rgb[0]]]])
        hsv_color = cv2.cvtColor(rgb_normalized, cv2.COLOR_BGR2HSV)

        # Create HSV range with tolerance for better detection
        tolerance = 15
        self.lower_hsv = np.array([
            max(0, hsv_color[0][0][0] - tolerance),
            max(0, hsv_color[0][0][1] - 50),
            max(0, hsv_color[0][0][2] - 50)
        ])
        self.upper_hsv = np.array([
            min(179, hsv_color[0][0][0] + tolerance),
            255,
            255
        ])

        # Anti-detection settings
        self.detection_threshold = 100  # Minimum pixels to trigger
        self.fire_delay_min = 0.01  # Minimum delay between shots
        self.fire_delay_max = 0.03  # Maximum delay between shots
        self.scan_delay = 0.001  # Delay between scans

        # Screen capture optimization
        self.monitor = {"top": 0, "left": 0, "width": 1920, "height": 1080}
        self.sct = mss.mss()

        # State tracking
        self.is_running = False
        self.last_fire_time = 0

        # Get Valorant process
        self.valorant_process = None
        self.find_valorant_process()

    def find_valorant_process(self):
        """Find Valorant process using psutil"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    if proc.info['name'] and 'VALORANT' in proc.info['name'].upper():
                        self.valorant_process = proc
                        print(f"Found Valorant process: {proc.info['name']} (PID: {proc.info['pid']})")
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            print(f"Error finding Valorant process: {e}")

        print("Valorant process not found - script will work regardless")
        return False

    def is_valorant_active(self):
        """Check if Valorant is running"""
        try:
            # Check if Valorant process exists and is running
            for proc in psutil.process_iter(['name']):
                if proc.info['name'] and 'VALORANT' in proc.info['name'].upper():
                    return True
            return False
        except:
            return True  # Assume active if check fails

    def professional_fire_method_1(self):
        """Method 1: Direct Windows API using ctypes"""
        try:
            # Define Windows API constants
            MOUSEEVENTF_LEFTDOWN = 0x0002
            MOUSEEVENTF_LEFTUP = 0x0004

            # Get user32.dll
            user32 = ctypes.windll.user32

            # Random micro-movement for human-like behavior
            if random.random() < 0.25:
                current_x, current_y = pyautogui.position()
                micro_x = random.randint(-1, 1)
                micro_y = random.randint(-1, 1)
                user32.SetCursorPos(current_x + micro_x, current_y + micro_y)
                time.sleep(random.uniform(0.001, 0.002))

            # Perform click using Windows API
            user32.mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            time.sleep(random.uniform(0.008, 0.025))  # Human-like hold duration
            user32.mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)

            return True
        except Exception as e:
            print(f"Method 1 failed: {e}")
            return False

    def professional_fire_method_2(self):
        """Method 2: Using mouse library with advanced timing"""
        try:
            # Random pre-click delay
            time.sleep(random.uniform(0.001, 0.003))

            # Use mouse library for more natural clicking
            mouse.press(button='left')
            time.sleep(random.uniform(0.012, 0.028))  # Variable hold time
            mouse.release(button='left')

            return True
        except Exception as e:
            print(f"Method 2 failed: {e}")
            return False

    def professional_fire_method_3(self):
        """Method 3: PyAutoGUI with advanced humanization"""
        try:
            # Store original settings
            original_pause = pyautogui.PAUSE


            # Set optimized settings
            pyautogui.PAUSE = 0

            # Add human-like jitter
            if random.random() < 0.2:
                jitter_x = random.uniform(-0.5, 0.5)
                jitter_y = random.uniform(-0.5, 0.5)
                current_x, current_y = pyautogui.position()
                pyautogui.moveTo(current_x + jitter_x, current_y + jitter_y, duration=0.001)

            # Perform click with variable duration
            click_duration = random.uniform(0.01, 0.025)
            pyautogui.mouseDown(button='left')
            time.sleep(click_duration)
            pyautogui.mouseUp(button='left')

            # Restore settings
            pyautogui.PAUSE = original_pause

            return True
        except Exception as e:
            print(f"Method 3 failed: {e}")
            return False

    def professional_fire_method_4(self):
        """Method 4: Keyboard simulation (for weapons that can be fired with keyboard)"""
        try:
            # Some players bind fire to keyboard keys
            # This method uses keyboard library for firing
            fire_key = 'space'  # Change this to your fire key if bound

            keyboard.press(fire_key)
            time.sleep(random.uniform(0.015, 0.035))
            keyboard.release(fire_key)

            return True
        except Exception as e:
            print(f"Method 4 failed: {e}")
            return False

    def smart_fire_system(self):
        """Intelligent firing system that tries multiple methods"""
        current_time = time.time()

        # Check fire rate limiting
        if current_time - self.last_fire_time < random.uniform(self.fire_delay_min, self.fire_delay_max):
            return False

        # Try firing methods in order of preference
        methods = [
            self.professional_fire_method_1,  # Windows API
            self.professional_fire_method_2,  # Mouse library
            self.professional_fire_method_3,  # Enhanced PyAutoGUI
            # self.professional_fire_method_4,  # Keyboard (uncomment if needed)
        ]

        for i, method in enumerate(methods):
            try:
                if method():
                    self.last_fire_time = current_time
                    if random.random() < 0.1:  # 10% chance to print success
                        print(f"🔥 Fired using method {i+1}")
                    return True
            except Exception as e:
                continue

        print("⚠️ All firing methods failed")
        return False

    def capture_screen_optimized(self):
        """Optimized screen capture using mss"""
        try:
            # Capture only the center area where enemies are likely to appear
            center_region = {
                "top": self.monitor["height"] // 4,
                "left": self.monitor["width"] // 4,
                "width": self.monitor["width"] // 2,
                "height": self.monitor["height"] // 2
            }

            screenshot = self.sct.grab(center_region)
            frame = np.array(screenshot)
            return cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
        except:
            # Fallback to pyautogui if mss fails
            screenshot = pyautogui.screenshot()
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

    def detect_target_color(self, frame):
        """Detect the target color #d3f150 in the frame"""
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

        # Create mask for target color
        mask = cv2.inRange(hsv, self.lower_hsv, self.upper_hsv)

        # Apply morphological operations to reduce noise
        kernel = np.ones((3, 3), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)

        # Count detected pixels
        detected_pixels = cv2.countNonZero(mask)

        return detected_pixels >= self.detection_threshold, detected_pixels

    def run_detection_loop(self):
        """Main detection loop with anti-detection measures"""
        print("Starting undetected auto-fire...")
        print(f"Target color: #{self.target_rgb[0]:02x}{self.target_rgb[1]:02x}{self.target_rgb[2]:02x}")
        print("Press Ctrl+C to stop")

        consecutive_detections = 0
        max_consecutive = 5  # Prevent spam clicking

        try:
            while self.is_running:
                # Only run when Valorant is active
                if not self.is_valorant_active():
                    time.sleep(0.1)
                    continue

                # Capture screen
                frame = self.capture_screen_optimized()

                # Detect target color
                target_detected, pixel_count = self.detect_target_color(frame)

                if target_detected:
                    consecutive_detections += 1

                    # Fire only if we haven't exceeded consecutive limit
                    if consecutive_detections <= max_consecutive:
                        if self.smart_fire_system():
                            print(f"🎯 Target detected! Pixels: {pixel_count} | Shots: {consecutive_detections}")

                    # Add slight delay after detection to avoid spam
                    time.sleep(random.uniform(0.005, 0.015))
                else:
                    consecutive_detections = 0

                # Random scan delay for anti-detection
                time.sleep(self.scan_delay + random.uniform(0, 0.002))

        except KeyboardInterrupt:
            print("\n🛑 Auto-fire stopped by user")
        except Exception as e:
            print(f"❌ Error in detection loop: {e}")

    def start(self):
        """Start the auto-fire system"""
        if self.is_running:
            print("Auto-fire is already running!")
            return

        self.is_running = True
        detection_thread = threading.Thread(target=self.run_detection_loop, daemon=True)
        detection_thread.start()

        try:
            detection_thread.join()
        except KeyboardInterrupt:
            self.stop()

    def stop(self):
        """Stop the auto-fire system"""
        self.is_running = False
        print("🛑 Auto-fire stopped")

# Usage
if __name__ == "__main__":
    auto_fire = UndetectedAutoFire()

    print("🎮 Undetected Valorant Auto-Fire")
    print("=" * 40)
    print("Target Color: #d3f150 (Valorant enemy highlight)")
    print("Features:")
    print("- Human-like clicking patterns")
    print("- Anti-detection randomization")
    print("- Optimized screen capture")
    print("- Valorant window detection")
    print("=" * 40)

    input("Press Enter to start auto-fire...")
    auto_fire.start()
