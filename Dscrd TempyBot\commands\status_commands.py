import discord
from discord.ext import commands
import requests
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category, is_channel_owner_or_manager

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None
TOKEN = None

async def update_voice_status(ctx, *, status_text: str):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    """Set custom voice channel status (push to endpoint)."""

    # Check if user is in a voice channel
    if not ctx.author.voice or not ctx.author.voice.channel:
        embed = await create_response_embed(
            "❌ Not in Voice Channel",
            "You must be in a voice channel to use this command."
        )
        await ctx.reply(embed=embed, mention_author=False)
        return

    channel = ctx.author.voice.channel

    # Check if the voice channel was created by the bot
    if channel.id not in bot_created_channels:
        embed = await create_response_embed(
            "❌ Error",
            "Command works only in a `BɅD ZONE | Tempy` VC."
        )
        await ctx.reply(embed=embed, mention_author=False)
        return

    # Proceed with status update
    url = f"https://discord.com/api/v10/channels/{channel.id}/voice-status"
    headers = {
        "Authorization": f"Bot {TOKEN}",
        "Content-Type": "application/json"
    }
    payload = {"status": status_text}

    res = requests.put(url, headers=headers, json=payload)

    if res.status_code in [200, 204]:
        embed = await create_response_embed(
            "Status successfully set",
            f"🗪 {ctx.author.mention} **Set status to** **`{status_text}`**"
        )
    else:
        embed = await create_response_embed(
            "❌ Failed to update",
            f"Status Code: {res.status_code}\nResponse: {res.text}"
        )

    await ctx.reply(embed=embed, mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name="status")
    @commands.check(is_in_target_category)
    async def status_command(ctx, *, status_text: str):
        await update_voice_status(ctx, status_text=status_text) 