"""
Professional Undetected Auto-Fire for Valorant
Advanced color detection with multiple firing methods
"""

import pyautogui
import cv2
import numpy as np
import time
import random
import threading
import mss
import ctypes
import keyboard
import mouse
import psutil
from aim_config import AimConfig, hex_to_rgb, DETECTION_ZONES

# Disable pyautogui failsafe
pyautogui.FAILSAFE = False

class ProfessionalAutoFire:
    def __init__(self, config=None):
        self.config = config or AimConfig()
        
        # Convert target color
        self.target_rgb = hex_to_rgb(self.config.TARGET_COLOR_HEX)
        
        # Setup HSV color range
        self.setup_color_detection()
        
        # Initialize screen capture with error handling
        self.sct = None
        self.init_screen_capture()
        self.setup_screen_capture()
        
        # State management
        self.is_running = False
        self.last_fire_time = 0
        self.consecutive_shots = 0
        self.current_method = 0
        
        # Performance tracking
        self.shots_fired = 0
        self.targets_detected = 0
        
        print("🎯 Professional Auto-Fire Initialized")
        print(f"Target Color: {self.config.TARGET_COLOR_HEX}")

    def init_screen_capture(self):
        """Initialize screen capture with error handling"""
        try:
            self.sct = mss.mss()
            print("✅ MSS screen capture initialized")
        except Exception as e:
            print(f"⚠️ MSS initialization failed: {e}")
            print("Will use PyAutoGUI as fallback")
            self.sct = None
            self.config.USE_OPTIMIZED_CAPTURE = False

    def setup_color_detection(self):
        """Setup HSV color range for target detection"""
        rgb_array = np.uint8([[[self.target_rgb[2], self.target_rgb[1], self.target_rgb[0]]]])
        hsv_color = cv2.cvtColor(rgb_array, cv2.COLOR_BGR2HSV)
        
        tolerance = self.config.COLOR_TOLERANCE
        self.lower_hsv = np.array([
            max(0, hsv_color[0][0][0] - tolerance),
            max(0, hsv_color[0][0][1] - 50),
            max(0, hsv_color[0][0][2] - 50)
        ])
        self.upper_hsv = np.array([
            min(179, hsv_color[0][0][0] + tolerance),
            255,
            255
        ])
    
    def setup_screen_capture(self):
        """Setup optimized screen capture region"""
        monitors = self.sct.monitors
        primary_monitor = monitors[1] if len(monitors) > 1 else monitors[0]
        
        if self.config.CAPTURE_CENTER_ONLY:
            width = int(primary_monitor["width"] * self.config.CAPTURE_WIDTH_RATIO)
            height = int(primary_monitor["height"] * self.config.CAPTURE_HEIGHT_RATIO)
            left = (primary_monitor["width"] - width) // 2
            top = (primary_monitor["height"] - height) // 2
            
            self.capture_region = {
                "top": top,
                "left": left,
                "width": width,
                "height": height
            }
        else:
            self.capture_region = primary_monitor
    
    def is_valorant_running(self):
        """Check if Valorant is running"""
        if not self.config.REQUIRE_VALORANT_ACTIVE:
            return True
            
        try:
            for proc in psutil.process_iter(['name']):
                if proc.info['name'] and 'VALORANT' in proc.info['name'].upper():
                    return True
            return False
        except:
            return True
    
    def fire_method_ctypes(self):
        """Method 1: Direct Windows API using ctypes"""
        try:
            user32 = ctypes.windll.user32
            
            # Micro-movement
            if random.random() < self.config.MICRO_MOVEMENT_CHANCE:
                current_pos = pyautogui.position()
                dx = random.randint(-self.config.MICRO_MOVEMENT_RANGE, self.config.MICRO_MOVEMENT_RANGE)
                dy = random.randint(-self.config.MICRO_MOVEMENT_RANGE, self.config.MICRO_MOVEMENT_RANGE)
                user32.SetCursorPos(current_pos[0] + dx, current_pos[1] + dy)
                time.sleep(random.uniform(0.001, 0.002))
            
            # Click
            user32.mouse_event(0x0002, 0, 0, 0, 0)  # MOUSEEVENTF_LEFTDOWN
            time.sleep(random.uniform(0.008, 0.025))
            user32.mouse_event(0x0004, 0, 0, 0, 0)  # MOUSEEVENTF_LEFTUP
            
            return True
        except:
            return False
    
    def fire_method_mouse_lib(self):
        """Method 2: Using mouse library"""
        try:
            mouse.press(button='left')
            time.sleep(random.uniform(0.012, 0.028))
            mouse.release(button='left')
            return True
        except:
            return False
    
    def fire_method_pyautogui(self):
        """Method 3: Enhanced PyAutoGUI"""
        try:
            pyautogui.PAUSE = 0
            
            if random.random() < self.config.JITTER_CHANCE:
                current_pos = pyautogui.position()
                jitter_x = random.uniform(-0.5, 0.5)
                jitter_y = random.uniform(-0.5, 0.5)
                pyautogui.moveTo(current_pos[0] + jitter_x, current_pos[1] + jitter_y, duration=0.001)
            
            pyautogui.mouseDown(button='left')
            time.sleep(random.uniform(0.01, 0.025))
            pyautogui.mouseUp(button='left')
            
            return True
        except:
            return False
    
    def execute_fire(self):
        """Execute firing with method rotation and anti-detection"""
        current_time = time.time()
        
        # Rate limiting
        min_delay = random.uniform(self.config.FIRE_DELAY_MIN, self.config.FIRE_DELAY_MAX)
        if current_time - self.last_fire_time < min_delay:
            return False
        
        # Consecutive shot limiting
        if self.consecutive_shots >= self.config.MAX_CONSECUTIVE_SHOTS:
            time.sleep(random.uniform(0.1, 0.3))  # Longer pause
            self.consecutive_shots = 0
            return False
        
        # Random pause for humanization
        if random.random() < self.config.RANDOM_PAUSE_CHANCE:
            pause_duration = random.uniform(*self.config.RANDOM_PAUSE_DURATION)
            time.sleep(pause_duration)
        
        # Try firing methods
        methods = [
            self.fire_method_ctypes,
            self.fire_method_mouse_lib,
            self.fire_method_pyautogui
        ]
        
        if self.config.METHOD_ROTATION:
            # Rotate methods for better stealth
            method = methods[self.current_method % len(methods)]
            self.current_method += 1
        else:
            # Try methods in order until one works
            method = None
            for m in methods:
                try:
                    if m():
                        method = m
                        break
                except:
                    continue
        
        if method and (not self.config.METHOD_ROTATION or method()):
            self.last_fire_time = current_time
            self.consecutive_shots += 1
            self.shots_fired += 1
            return True
        
        return False
    
    def detect_target(self, frame):
        """Advanced target detection with noise reduction"""
        # Convert to HSV
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Create mask
        mask = cv2.inRange(hsv, self.lower_hsv, self.upper_hsv)
        
        # Noise reduction
        if self.config.NOISE_REDUCTION:
            kernel = np.ones((3, 3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        # Count pixels
        detected_pixels = cv2.countNonZero(mask)
        
        return detected_pixels >= self.config.DETECTION_THRESHOLD, detected_pixels
    
    def capture_screen(self):
        """Robust screen capture with multiple fallback methods"""
        # Method 1: Try MSS with error handling
        if self.config.USE_OPTIMIZED_CAPTURE:
            try:
                # Reinitialize MSS if needed
                if not hasattr(self, 'sct') or self.sct is None:
                    self.sct = mss.mss()

                screenshot = self.sct.grab(self.capture_region)
                frame = np.array(screenshot)
                return cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
            except Exception as e:
                print(f"MSS capture failed: {e}")
                # Try to reinitialize MSS
                try:
                    self.sct.close()
                    self.sct = mss.mss()
                    screenshot = self.sct.grab(self.capture_region)
                    frame = np.array(screenshot)
                    return cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
                except:
                    print("MSS reinitialize failed, switching to PyAutoGUI")
                    self.config.USE_OPTIMIZED_CAPTURE = False

        # Method 2: PyAutoGUI fallback
        try:
            if hasattr(self, 'capture_region') and self.config.CAPTURE_CENTER_ONLY:
                # Capture specific region with PyAutoGUI
                screenshot = pyautogui.screenshot(region=(
                    self.capture_region['left'],
                    self.capture_region['top'],
                    self.capture_region['width'],
                    self.capture_region['height']
                ))
            else:
                # Full screen capture
                screenshot = pyautogui.screenshot()

            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            print(f"PyAutoGUI capture failed: {e}")

        # Method 3: Alternative MSS approach
        try:
            with mss.mss() as sct:
                screenshot = sct.grab(self.capture_region)
                frame = np.array(screenshot)
                return cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
        except Exception as e:
            print(f"Alternative MSS failed: {e}")

        print("❌ All capture methods failed")
        return None
    
    def main_loop(self):
        """Main detection and firing loop"""
        print("🚀 Starting professional auto-fire...")
        print("Press Ctrl+Shift+Q to stop")
        
        # Setup emergency stop
        keyboard.add_hotkey(self.config.EMERGENCY_STOP_KEY, self.stop)
        
        try:
            while self.is_running:
                # Check if Valorant is running
                if not self.is_valorant_running():
                    time.sleep(0.1)
                    continue
                
                # Capture screen
                frame = self.capture_screen()
                if frame is None:
                    continue
                
                # Detect target
                target_detected, pixel_count = self.detect_target(frame)
                
                if target_detected:
                    self.targets_detected += 1
                    
                    if self.execute_fire():
                        if self.shots_fired % 10 == 0:  # Print every 10 shots
                            print(f"🎯 Shots: {self.shots_fired} | Targets: {self.targets_detected} | Pixels: {pixel_count}")
                else:
                    self.consecutive_shots = 0  # Reset consecutive counter
                
                # Dynamic scan delay
                base_delay = self.config.SCAN_DELAY_BASE
                random_delay = random.uniform(0, self.config.SCAN_DELAY_RANDOM)
                time.sleep(base_delay + random_delay)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopped by user")
        except Exception as e:
            print(f"❌ Error in main loop: {e}")
        finally:
            keyboard.unhook_all()
    
    def start(self):
        """Start the auto-fire system"""
        if self.is_running:
            print("Auto-fire is already running!")
            return
        
        self.is_running = True
        self.shots_fired = 0
        self.targets_detected = 0
        
        # Run in separate thread
        self.thread = threading.Thread(target=self.main_loop, daemon=True)
        self.thread.start()
        
        try:
            self.thread.join()
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self):
        """Stop the auto-fire system"""
        self.is_running = False

        # Cleanup screen capture
        try:
            if hasattr(self, 'sct') and self.sct is not None:
                self.sct.close()
                self.sct = None
        except:
            pass

        print(f"\n📊 Session Stats:")
        print(f"Shots Fired: {self.shots_fired}")
        print(f"Targets Detected: {self.targets_detected}")
        print("🛑 Auto-fire stopped")

if __name__ == "__main__":
    # Create and configure auto-fire
    auto_fire = ProfessionalAutoFire()
    
    print("🎮 Professional Valorant Auto-Fire")
    print("=" * 50)
    print("🎯 Features:")
    print("- Multiple firing methods with rotation")
    print("- Advanced color detection for #d3f150")
    print("- Human-like behavior simulation")
    print("- Anti-detection randomization")
    print("- Performance optimizations")
    print("- Emergency stop: Ctrl+Shift+Q")
    print("=" * 50)
    
    input("Press Enter to start...")
    auto_fire.start()
