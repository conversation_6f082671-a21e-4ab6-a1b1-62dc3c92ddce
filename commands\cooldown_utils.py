import discord
import time

# These will be set by main.py
bot = None
user_cooldowns = {}
TARGET_CATEGORY_NAME = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def create_response_embed(title, description, color=0x99AAB5):
    embed = discord.Embed(
        title=title,
        description=description,
        color=0x5E6161
    )
    if bot.user.avatar:
        embed.set_author(name=bot.user.name, icon_url=bot.user.avatar.url)
        embed.set_thumbnail(url=bot.user.avatar.url)
    else:
        embed.set_author(name=bot.user.name, icon_url=bot.user.default_avatar.url)
        embed.set_thumbnail(url=bot.user.default_avatar.url)

    return embed

async def check_cooldown(ctx):
    user_id = ctx.author.id
    current_time = time.time()
    
    # Initialize user in cooldown dictionary if not present
    if user_id not in user_cooldowns:
        user_cooldowns[user_id] = {'last_commands': [], 'on_cooldown_until': 0}
    
    # Check if user is currently on cooldown
    if user_cooldowns[user_id]['on_cooldown_until'] > current_time:
        remaining_time = int(user_cooldowns[user_id]['on_cooldown_until'] - current_time)
        embed = await create_response_embed("Cooldown Active", f"{ctx.author.mention} You're on cooldown! Please wait {remaining_time} seconds.")
        await ctx.reply(embed=embed, mention_author=False)
        return True
    
    # Update command history, keeping only recent commands
    user_cooldowns[user_id]['last_commands'] = [
        timestamp for timestamp in user_cooldowns[user_id]['last_commands'] 
        if current_time - timestamp < 60  # 60 second window
    ]
    
    # Add current command timestamp
    user_cooldowns[user_id]['last_commands'].append(current_time)
    
    # Check if threshold is reached (4 commands in 60 seconds)
    if len(user_cooldowns[user_id]['last_commands']) >= 4:
        # Set cooldown for 270 seconds (4.5 minutes)
        user_cooldowns[user_id]['on_cooldown_until'] = current_time + 270
        user_cooldowns[user_id]['last_commands'] = []  # Clear command history
        embed = await create_response_embed("Cooldown Triggered", f"{ctx.author.mention} You're on cooldown for 270 seconds.")
        await ctx.reply(embed=embed, mention_author=False)
        return True
    
    return False  # Not on cooldown

def is_in_target_category(ctx):
    # If in DM, return False
    if not ctx.guild:
        return False
   
    # Check if the text channel itself is in the target category
    if ctx.channel.category and ctx.channel.category.name == TARGET_CATEGORY_NAME:
        return True
   
    # Get the voice channel the message is related to
    voice_channel = None
   
    # If the message is directly in a voice channel
    if isinstance(ctx.channel, discord.VoiceChannel):
        voice_channel = ctx.channel
   
    # If this is the text chat of a voice channel
    elif hasattr(ctx.channel, 'related_voice_channel') and ctx.channel.related_voice_channel:
        voice_channel = ctx.channel.related_voice_channel
   
    # Check if we found a voice channel and if it's in the target category
    if voice_channel and voice_channel.category:
        if voice_channel.category.name == TARGET_CATEGORY_NAME:
            return True
   
    return False

async def is_channel_owner_or_manager(ctx) -> bool:
    if not ctx.author.voice or not ctx.author.voice.channel:
        embed = await create_response_embed("Error", "You need to be in a voice channel to use this command!")
        await ctx.reply(embed=embed, mention_author=False)
        return False
    
    channel = ctx.author.voice.channel
    owner = channel_owners.get(channel.id)
    managers = channel_managers.get(channel.id, set())
    
    if channel.id not in bot_created_channels:
        embed = await create_response_embed("Error", "Command works only in a `DeathZone | Tempy` VC")
        await ctx.reply(embed=embed, mention_author=False)
        return False
    
    # Allow if the user is the owner or a manager
    if owner == ctx.author or ctx.author in managers:
        return True
    else:
        embed = await create_response_embed("Error", "You are not the owner or a manager of this channel!")
        await ctx.reply(embed=embed, mention_author=False)
        return False

async def is_channel_owner(ctx) -> bool:
    if not ctx.author.voice or not ctx.author.voice.channel:
        embed = await create_response_embed("Error", "You need to be in a voice channel to use this command!")
        await ctx.reply(embed=embed, mention_author=False)
        return False
    
    channel = ctx.author.voice.channel
    owner = channel_owners.get(channel.id)
    
    if channel.id not in bot_created_channels:
        embed = await create_response_embed("Error", "Command works only in a `DeathZone | Tempy` VC")
        await ctx.reply(embed=embed, mention_author=False)
        return False
    
    # Allow only if the user is the owner
    if owner == ctx.author:
        return True
    else:
        embed = await create_response_embed("Error", "Only the channel owner can use this command!")
        await ctx.reply(embed=embed, mention_author=False)
        return False

async def check_not_targeting_owner(ctx, target_member):
    channel = ctx.author.voice.channel
    owner = channel_owners.get(channel.id)
    
    # If the target is the owner and the command issuer is not the owner
    if target_member == owner and ctx.author != owner:
        embed = await create_response_embed("Error", "You cannot use this command on the channel owner!")
        await ctx.reply(embed=embed, mention_author=False)
        return False
    return True 