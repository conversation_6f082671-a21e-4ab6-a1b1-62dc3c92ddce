"""
Simple AI Aimbot for Valorant
Automatically tracks enemies with smooth mouse movement
NO FIRING - Only mouse tracking
"""

import cv2
import numpy as np
import pyautogui
import time
import threading
import ctypes
import math
import random
import psutil

# Disable pyautogui failsafe
pyautogui.FAILSAFE = False

class SimpleAimbot:
    def __init__(self):
        # Screen setup
        self.screen_width, self.screen_height = pyautogui.size()
        self.screen_center_x = self.screen_width // 2
        self.screen_center_y = self.screen_height // 2
        
        # Enemy color detection (#d3f150)
        self.target_rgb = (211, 241, 80)
        self.setup_color_detection()
        
        # Detection area (center 70% of screen)
        detection_ratio = 0.7
        self.detection_width = int(self.screen_width * detection_ratio)
        self.detection_height = int(self.screen_height * detection_ratio)
        self.detection_left = (self.screen_width - self.detection_width) // 2
        self.detection_top = (self.screen_height - self.detection_height) // 2
        
        # Aimbot settings
        self.smoothing = 0.15  # How smooth the aim is (0.1 = very smooth, 0.5 = snappy)
        self.max_speed = 15  # Maximum pixels to move per frame
        self.min_distance = 3  # Minimum distance to move mouse
        
        # Humanization
        self.human_error_chance = 0.05  # 5% chance of slight error
        self.micro_adjust_chance = 0.1  # 10% chance of micro adjustment
        
        # State
        self.is_running = False
        self.targets_found = 0
        self.movements_made = 0
        
        print("🎯 Simple AI Aimbot Initialized")
        print(f"Screen: {self.screen_width}x{self.screen_height}")
        print(f"Detection Area: {self.detection_width}x{self.detection_height}")
        print(f"Smoothing: {self.smoothing} | Max Speed: {self.max_speed}px/frame")
    
    def setup_color_detection(self):
        """Setup color detection for enemies"""
        rgb_array = np.uint8([[[self.target_rgb[2], self.target_rgb[1], self.target_rgb[0]]]])
        hsv_color = cv2.cvtColor(rgb_array, cv2.COLOR_BGR2HSV)
        
        tolerance = 12
        self.lower_hsv = np.array([
            max(0, hsv_color[0][0][0] - tolerance),
            max(0, hsv_color[0][0][1] - 30),
            max(0, hsv_color[0][0][2] - 30)
        ])
        self.upper_hsv = np.array([
            min(179, hsv_color[0][0][0] + tolerance),
            255,
            255
        ])
    
    def is_valorant_running(self):
        """Check if Valorant is running"""
        try:
            for proc in psutil.process_iter(['name']):
                if proc.info['name'] and 'VALORANT' in proc.info['name'].upper():
                    return True
            return False
        except:
            return True
    
    def capture_detection_area(self):
        """Capture the screen area where we look for enemies"""
        try:
            screenshot = pyautogui.screenshot(region=(
                self.detection_left,
                self.detection_top,
                self.detection_width,
                self.detection_height
            ))
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            print(f"Capture error: {e}")
            return None
    
    def find_enemies(self, frame):
        """Find enemies in the frame"""
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Create mask for enemy color
        mask = cv2.inRange(hsv, self.lower_hsv, self.upper_hsv)
        
        # Clean up the mask
        kernel = np.ones((3, 3), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        # Find enemy shapes
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        enemies = []
        for contour in contours:
            area = cv2.contourArea(contour)
            
            # Only consider larger detections (real enemies)
            if area > 40:
                # Get the center of the enemy
                x, y, w, h = cv2.boundingRect(contour)
                center_x = x + w // 2
                center_y = y + h // 2
                
                # Convert to screen coordinates
                screen_x = center_x + self.detection_left
                screen_y = center_y + self.detection_top
                
                enemies.append({
                    'x': screen_x,
                    'y': screen_y,
                    'size': area
                })
        
        # Sort by size (bigger enemies first)
        enemies.sort(key=lambda e: e['size'], reverse=True)
        return enemies
    
    def choose_target(self, enemies):
        """Choose the best enemy to aim at"""
        if not enemies:
            return None
        
        current_x, current_y = pyautogui.position()
        
        best_enemy = None
        best_score = -1
        
        for enemy in enemies:
            # Calculate distance from current mouse position
            distance = math.sqrt((enemy['x'] - current_x)**2 + (enemy['y'] - current_y)**2)
            
            # Calculate distance from screen center
            center_distance = math.sqrt((enemy['x'] - self.screen_center_x)**2 + (enemy['y'] - self.screen_center_y)**2)
            
            # Score based on distance (closer = better)
            distance_score = 1.0 / (1.0 + distance / 100.0)
            center_score = 1.0 / (1.0 + center_distance / 200.0)
            size_score = min(enemy['size'] / 500.0, 1.0)
            
            # Combined score
            total_score = distance_score * 0.5 + center_score * 0.3 + size_score * 0.2
            
            if total_score > best_score:
                best_score = total_score
                best_enemy = enemy
        
        return best_enemy
    
    def move_mouse_to_target(self, target_x, target_y):
        """Smoothly move mouse to target"""
        current_x, current_y = pyautogui.position()
        
        # Calculate how far to move
        dx = target_x - current_x
        dy = target_y - current_y
        distance = math.sqrt(dx**2 + dy**2)
        
        # Don't move if target is very close
        if distance < self.min_distance:
            return False
        
        # Apply smoothing (only move part of the way)
        move_x = dx * self.smoothing
        move_y = dy * self.smoothing
        
        # Limit maximum speed
        move_distance = math.sqrt(move_x**2 + move_y**2)
        if move_distance > self.max_speed:
            scale = self.max_speed / move_distance
            move_x *= scale
            move_y *= scale
        
        # Add human-like behavior
        if random.random() < self.micro_adjust_chance:
            move_x += random.uniform(-0.5, 0.5)
            move_y += random.uniform(-0.5, 0.5)
        
        if random.random() < self.human_error_chance:
            move_x += random.uniform(-1.5, 1.5)
            move_y += random.uniform(-1.5, 1.5)
        
        # Move the mouse
        try:
            # Use Windows API for smooth movement
            user32 = ctypes.windll.user32
            new_x = int(current_x + move_x)
            new_y = int(current_y + move_y)
            user32.SetCursorPos(new_x, new_y)
            self.movements_made += 1
            return True
        except:
            # Fallback to pyautogui
            pyautogui.moveTo(current_x + move_x, current_y + move_y, duration=0)
            self.movements_made += 1
            return True
    
    def main_loop(self):
        """Main aimbot loop"""
        print("🚀 Simple AI Aimbot Active")
        print("Your mouse will automatically track enemies!")
        print("Press Ctrl+C to stop")
        
        try:
            while self.is_running:
                # Check if Valorant is running
                if not self.is_valorant_running():
                    time.sleep(0.1)
                    continue
                
                # Capture screen
                frame = self.capture_detection_area()
                if frame is None:
                    continue
                
                # Find enemies
                enemies = self.find_enemies(frame)
                
                if enemies:
                    # Choose best target
                    target = self.choose_target(enemies)
                    
                    if target:
                        self.targets_found += 1
                        
                        # Move mouse to target
                        if self.move_mouse_to_target(target['x'], target['y']):
                            if self.targets_found % 50 == 0:  # Print every 50 targets
                                print(f"🎯 Targets: {self.targets_found} | Movements: {self.movements_made}")
                
                # Small delay for performance
                time.sleep(0.016)  # ~60 FPS
                
        except KeyboardInterrupt:
            print("\n🛑 Stopped by user")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    def start(self):
        """Start the aimbot"""
        if self.is_running:
            print("Aimbot is already running!")
            return
        
        self.is_running = True
        self.targets_found = 0
        self.movements_made = 0
        
        # Run in separate thread
        thread = threading.Thread(target=self.main_loop, daemon=True)
        thread.start()
        
        try:
            thread.join()
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self):
        """Stop the aimbot"""
        self.is_running = False
        
        print(f"\n📊 Session Stats:")
        print(f"Targets Found: {self.targets_found}")
        print(f"Mouse Movements: {self.movements_made}")
        if self.targets_found > 0:
            efficiency = self.movements_made / self.targets_found
            print(f"Movement Efficiency: {efficiency:.1f} moves per target")
        print("🛑 Simple AI Aimbot stopped")

if __name__ == "__main__":
    aimbot = SimpleAimbot()
    
    print("🤖 Simple AI Aimbot for Valorant")
    print("=" * 40)
    print("🎯 Features:")
    print("- Automatic enemy detection")
    print("- Smooth mouse tracking")
    print("- Human-like movement")
    print("- NO FIRING - Only aiming")
    print("- Easy to use")
    print("=" * 40)
    print("🔧 Settings:")
    print(f"- Smoothing: {aimbot.smoothing}")
    print(f"- Max Speed: {aimbot.max_speed} px/frame")
    print(f"- Detection Area: 70% of screen")
    print("=" * 40)
    print("⚠️  Instructions:")
    print("1. Start Valorant")
    print("2. Mouse will track enemies automatically")
    print("3. You manually fire when ready")
    print("4. Press Ctrl+C to stop")
    print("=" * 40)
    
    input("Press Enter to start simple aimbot...")
    aimbot.start()
