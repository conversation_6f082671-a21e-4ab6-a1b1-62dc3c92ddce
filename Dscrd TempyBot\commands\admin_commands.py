import discord
from discord.ext import commands
import time
from .cooldown_utils import create_response_embed

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None
unknown_command_cooldowns = None

async def show_cooldowns(ctx):
    """Check list of users on cooldown (admin only)"""
    # Check if user is admin (you can modify this check based on your admin system)
    if not ctx.author.guild_permissions.administrator:
        embed = await create_response_embed("Error", "This command is for administrators only!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    if not unknown_command_cooldowns:
        embed = await create_response_embed("Cooldown List", "No users are currently on cooldown.")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    current_time = time.time()
    cooldown_list = []
    
    for user_id, cooldown_end in unknown_command_cooldowns.items():
        if current_time < cooldown_end:
            user = bot.get_user(user_id)
            if user:
                time_left = int((cooldown_end - current_time) / 60)  # Convert to minutes
                cooldown_list.append(f"**{user.display_name}** ({user.mention}) - {time_left} minutes left")
            else:
                time_left = int((cooldown_end - current_time) / 60)
                cooldown_list.append(f"**Unknown User** (ID: {user_id}) - {time_left} minutes left")
    
    if not cooldown_list:
        embed = await create_response_embed("Cooldown List", "No users are currently on cooldown.")
    else:
        description = "\n".join(cooldown_list)
        embed = discord.Embed(
            title="<:time:1257454120739016764> Users on Cooldown",
            description=description,
            color=0xff6b6b
        )
        embed.set_footer(text=f"Total users on cooldown: {len(cooldown_list)}")
    
    await ctx.reply(embed=embed, mention_author=False)

async def clear_cooldown(ctx, user: discord.Member = None):
    """Clear cooldown for a specific user (admin only)"""
    # Check if user is admin (you can modify this check based on your admin system)
    if not ctx.author.guild_permissions.administrator:
        embed = await create_response_embed("Error", "This command is for administrators only!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    if not user:
        embed = await create_response_embed("Error", "Please mention a user to clear their cooldown!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    user_id = user.id
    
    # Check if user is on cooldown
    if user_id not in unknown_command_cooldowns:
        embed = await create_response_embed("Error", f"{user.mention} is not on cooldown!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    # Clear the cooldown
    if user_id in unknown_command_cooldowns:
        del unknown_command_cooldowns[user_id]
    
    embed = await create_response_embed(
        "Cooldown Cleared",
        f"✅ Cooldown has been cleared for {user.mention}!"
    )
    await ctx.reply(embed=embed, mention_author=False)
    
    # Log the action
    print(f"Admin {ctx.author} cleared cooldown for {user} ({user_id})")

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='cldcheck')
    async def cldcheck_command(ctx):
        await show_cooldowns(ctx)
    
    @bot.command(name='cldclr')
    async def cldclr_command(ctx, user: discord.Member = None):
        await clear_cooldown(ctx, user) 