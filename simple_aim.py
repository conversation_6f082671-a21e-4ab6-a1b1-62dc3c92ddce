"""
Simple Professional Auto-Fire for Valorant
No MSS dependency - uses only PyAutoGUI and alternative methods
"""

import pyautogui
import cv2
import numpy as np
import time
import random
import threading
import ctypes
import keyboard
import mouse
import psutil

# Disable pyautogui failsafe
pyautogui.FAILSAFE = False

class SimpleAutoFire:
    def __init__(self):
        # Target color #d3f150 in RGB
        self.target_rgb = (211, 241, 80)
        
        # Setup color detection
        self.setup_color_detection()
        
        # Settings
        self.detection_threshold = 100
        self.fire_delay_min = 0.008
        self.fire_delay_max = 0.035
        self.max_consecutive_shots = 5
        self.scan_delay = 0.001
        
        # State
        self.is_running = False
        self.last_fire_time = 0
        self.consecutive_shots = 0
        self.shots_fired = 0
        self.current_method = 0
        
        # Screen region (center 60% of screen)
        screen_width, screen_height = pyautogui.size()
        self.region_width = int(screen_width * 0.6)
        self.region_height = int(screen_height * 0.6)
        self.region_left = (screen_width - self.region_width) // 2
        self.region_top = (screen_height - self.region_height) // 2
        
        print("🎯 Simple Auto-Fire Initialized")
        print(f"Target Color: #d3f150")
        print(f"Scan Region: {self.region_width}x{self.region_height}")
    
    def setup_color_detection(self):
        """Setup HSV color range"""
        rgb_array = np.uint8([[[self.target_rgb[2], self.target_rgb[1], self.target_rgb[0]]]])
        hsv_color = cv2.cvtColor(rgb_array, cv2.COLOR_BGR2HSV)
        
        tolerance = 15
        self.lower_hsv = np.array([
            max(0, hsv_color[0][0][0] - tolerance),
            max(0, hsv_color[0][0][1] - 50),
            max(0, hsv_color[0][0][2] - 50)
        ])
        self.upper_hsv = np.array([
            min(179, hsv_color[0][0][0] + tolerance),
            255,
            255
        ])
    
    def is_valorant_running(self):
        """Check if Valorant is running"""
        try:
            for proc in psutil.process_iter(['name']):
                if proc.info['name'] and 'VALORANT' in proc.info['name'].upper():
                    return True
            return False
        except:
            return True
    
    def fire_method_ctypes(self):
        """Method 1: Windows API with ctypes"""
        try:
            user32 = ctypes.windll.user32
            
            # Micro-movement (25% chance)
            if random.random() < 0.25:
                current_pos = pyautogui.position()
                dx = random.randint(-1, 1)
                dy = random.randint(-1, 1)
                user32.SetCursorPos(current_pos[0] + dx, current_pos[1] + dy)
                time.sleep(random.uniform(0.001, 0.002))
            
            # Click
            user32.mouse_event(0x0002, 0, 0, 0, 0)  # LEFTDOWN
            time.sleep(random.uniform(0.008, 0.025))
            user32.mouse_event(0x0004, 0, 0, 0, 0)  # LEFTUP
            
            return True
        except:
            return False
    
    def fire_method_mouse(self):
        """Method 2: Mouse library"""
        try:
            mouse.press(button='left')
            time.sleep(random.uniform(0.012, 0.028))
            mouse.release(button='left')
            return True
        except:
            return False
    
    def fire_method_pyautogui(self):
        """Method 3: PyAutoGUI"""
        try:
            pyautogui.PAUSE = 0
            
            # Jitter (20% chance)
            if random.random() < 0.2:
                current_pos = pyautogui.position()
                jitter_x = random.uniform(-0.5, 0.5)
                jitter_y = random.uniform(-0.5, 0.5)
                pyautogui.moveTo(current_pos[0] + jitter_x, current_pos[1] + jitter_y, duration=0.001)
            
            pyautogui.mouseDown(button='left')
            time.sleep(random.uniform(0.01, 0.025))
            pyautogui.mouseUp(button='left')
            
            return True
        except:
            return False
    
    def execute_fire(self):
        """Execute firing with method rotation"""
        current_time = time.time()
        
        # Rate limiting
        min_delay = random.uniform(self.fire_delay_min, self.fire_delay_max)
        if current_time - self.last_fire_time < min_delay:
            return False
        
        # Consecutive shot limiting
        if self.consecutive_shots >= self.max_consecutive_shots:
            time.sleep(random.uniform(0.1, 0.3))
            self.consecutive_shots = 0
            return False
        
        # Random pause (5% chance)
        if random.random() < 0.05:
            time.sleep(random.uniform(0.1, 0.3))
        
        # Try firing methods in rotation
        methods = [
            self.fire_method_ctypes,
            self.fire_method_mouse,
            self.fire_method_pyautogui
        ]
        
        method = methods[self.current_method % len(methods)]
        self.current_method += 1
        
        if method():
            self.last_fire_time = current_time
            self.consecutive_shots += 1
            self.shots_fired += 1
            return True
        
        return False
    
    def detect_target(self, frame):
        """Detect target color"""
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        mask = cv2.inRange(hsv, self.lower_hsv, self.upper_hsv)
        
        # Noise reduction
        kernel = np.ones((3, 3), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        detected_pixels = cv2.countNonZero(mask)
        return detected_pixels >= self.detection_threshold, detected_pixels
    
    def capture_screen(self):
        """Simple screen capture using PyAutoGUI"""
        try:
            screenshot = pyautogui.screenshot(region=(
                self.region_left,
                self.region_top,
                self.region_width,
                self.region_height
            ))
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            print(f"Capture error: {e}")
            return None
    
    def main_loop(self):
        """Main detection loop"""
        print("🚀 Starting simple auto-fire...")
        print("Press Ctrl+C to stop")
        
        targets_detected = 0
        
        try:
            while self.is_running:
                # Check Valorant
                if not self.is_valorant_running():
                    time.sleep(0.1)
                    continue
                
                # Capture screen
                frame = self.capture_screen()
                if frame is None:
                    continue
                
                # Detect target
                target_detected, pixel_count = self.detect_target(frame)
                
                if target_detected:
                    targets_detected += 1
                    
                    if self.execute_fire():
                        if self.shots_fired % 10 == 0:
                            print(f"🎯 Shots: {self.shots_fired} | Targets: {targets_detected}")
                else:
                    self.consecutive_shots = 0
                
                # Scan delay
                time.sleep(self.scan_delay + random.uniform(0, 0.002))
                
        except KeyboardInterrupt:
            print("\n🛑 Stopped by user")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    def start(self):
        """Start auto-fire"""
        if self.is_running:
            print("Already running!")
            return
        
        self.is_running = True
        self.shots_fired = 0
        
        thread = threading.Thread(target=self.main_loop, daemon=True)
        thread.start()
        
        try:
            thread.join()
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self):
        """Stop auto-fire"""
        self.is_running = False
        print(f"\n📊 Shots Fired: {self.shots_fired}")
        print("🛑 Auto-fire stopped")

if __name__ == "__main__":
    auto_fire = SimpleAutoFire()
    
    print("🎮 Simple Valorant Auto-Fire")
    print("=" * 40)
    print("🎯 Target: #d3f150 (enemy highlight)")
    print("🔥 3 Professional firing methods")
    print("🛡️ Anti-detection features")
    print("📱 No MSS dependency")
    print("=" * 40)
    
    input("Press Enter to start...")
    auto_fire.start()
