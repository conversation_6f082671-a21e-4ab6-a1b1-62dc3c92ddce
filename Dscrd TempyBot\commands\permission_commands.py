import discord
from discord.ext import commands
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category, is_channel_owner_or_manager, check_not_targeting_owner

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def permit(ctx, members: commands.Greedy[discord.Member]):
    if not await is_channel_owner_or_manager(ctx):
        return

    if not members:
        await ctx.reply("Please mention at least one user to permit.", mention_author=False)
        return

    channel = ctx.author.voice.channel
    for member in members:
        await channel.set_permissions(member, connect=True)

    mentions = ', '.join(member.mention for member in members)
    embed = await create_response_embed(
        "Access Granted",
        f"{ctx.author.mention} **Granted `Connect perm.` to** {mentions} ! ✅"
    )
    await ctx.reply(embed=embed, mention_author=False)

async def reject(ctx, members: commands.Greedy[discord.Member]):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
   
    if not members:
        await ctx.reply("Please mention at least one user to reject.", mention_author=False)
        return
   
    channel = ctx.author.voice.channel
    rejected_members = []
   
    for member in members:
        # Check if user is trying to target themselves
        if member == ctx.author:
            embed = await create_response_embed(
                "Invalid Target",
                "❌ You cannot reject yourself!"
            )
            await ctx.reply(embed=embed, mention_author=False)
            continue
            
        # Check if trying to reject the owner
        if not await check_not_targeting_owner(ctx, member):
            continue
       
        overwrites = channel.overwrites_for(member)
        overwrites.connect = False
        await channel.set_permissions(member, overwrite=overwrites)
       
        if member in channel.members:
            try:
                await member.move_to(None)
            except discord.HTTPException:
                pass
       
        rejected_members.append(member)
   
    if rejected_members:
        mentions = ', '.join(member.mention for member in rejected_members)
        embed = await create_response_embed(
            "Users Rejected ❌",
            f"{ctx.author.mention} **Removed `Connect perm.` from** {mentions}!"
        )
        await ctx.reply(embed=embed, mention_author=False)

async def text_mute(ctx, members: commands.Greedy[discord.Member]):
    if not await is_channel_owner_or_manager(ctx):
        return
    if not members:
        await ctx.reply("Please mention at least one user to text mute.", mention_author=False)
        return
    
    # Remove self from the list of members to mute
    members = [member for member in members if member != ctx.author]
    
    if not members:
        await ctx.reply("You cannot text mute yourself.", mention_author=False)
        return
    
    channel = ctx.author.voice.channel
    for member in members:
        await channel.set_permissions(member, send_messages=False)
    mentions = ', '.join(member.mention for member in members)
    embed = await create_response_embed(
        "Text Muted",
        f"{ctx.author.mention} **Removed `Send messages perm.` for** {mentions}! ⚠️"
    )
    await ctx.reply(embed=embed, mention_author=False)

async def text_unmute(ctx, members: commands.Greedy[discord.Member]):
    if not await is_channel_owner_or_manager(ctx):
        return
    if not members:
        await ctx.reply("Please mention at least one user to text unmute.", mention_author=False)
        return
    channel = ctx.author.voice.channel
    for member in members:
        await channel.set_permissions(member, send_messages=None)  # Reset to default
    mentions = ', '.join(member.mention for member in members)
    embed = await create_response_embed(
        "Text Unmuted",
        f"{ctx.author.mention} **Granted `Send messages perm` to** {mentions}.` ! ⚠️"
    )
    await ctx.reply(embed=embed, mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='perm')
    @commands.check(is_in_target_category)
    async def perm_command(ctx, members: commands.Greedy[discord.Member]):
        await permit(ctx, members)
    
    @bot.command(name='reject')
    @commands.check(is_in_target_category)
    async def reject_command(ctx, members: commands.Greedy[discord.Member]):
        await reject(ctx, members)
    
    @bot.command(name='tmute')
    @commands.check(is_in_target_category)
    async def tmute_command(ctx, members: commands.Greedy[discord.Member]):
        await text_mute(ctx, members)
    
    @bot.command(name='tunmute')
    @commands.check(is_in_target_category)
    async def tunmute_command(ctx, members: commands.Greedy[discord.Member]):
        await text_unmute(ctx, members) 