import discord
from discord.ext import commands
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category, is_channel_owner_or_manager

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def sboff(ctx):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return

    channel = ctx.author.voice.channel
    for member in channel.members:
        # Don't disable soundboard for the owner if a manager is using the command
        if member == channel_owners.get(channel.id) and ctx.author != member:
            continue
        await channel.set_permissions(member, use_soundboard=False)

    embed = await create_response_embed(
        "Soundboard Disabled",
        "The soundboard is disabled .🔇"
    )
    await ctx.reply(embed=embed, mention_author=False)

async def sbon(ctx):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return

    channel = ctx.author.voice.channel
    for member in channel.members:
        await channel.set_permissions(member, use_soundboard=True)

    embed = await create_response_embed(
        "Soundboard Enabled",
        "The soundboard is enabled . 🔊"
    )
    await ctx.reply(embed=embed, mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='sboff')
    @commands.check(is_in_target_category)
    async def sboff_command(ctx):
        await sboff(ctx)
    
    @bot.command(name='sbon')
    @commands.check(is_in_target_category)
    async def sbon_command(ctx):
        await sbon(ctx) 