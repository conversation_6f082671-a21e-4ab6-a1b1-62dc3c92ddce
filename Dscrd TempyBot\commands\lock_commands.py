import discord
from discord.ext import commands
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category, is_channel_owner_or_manager

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def lock(ctx):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return

    channel = ctx.author.voice.channel
     member_role = discord.utils.get(ctx.guild.roles, name="┆Member")

    if member_role:
        await channel.set_permissions(member_role, connect=False)
        for member in channel.members:
            await channel.set_permissions(member, connect=True)
        
        # Add lock emoji to the channel name
        if not channel.name.startswith("🔒"):
            await channel.edit(name=f"🔒 {channel.name}")
        
        embed = await create_response_embed("Channel Locked", f"{ctx.author.mention} **Successfully `locked` the channel** 🔒")
    else:
        embed = await create_response_embed("Error", "The @Member role was not found.")

    await ctx.reply(embed=embed, mention_author=False)

async def unlock(ctx):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    
    channel = ctx.author.voice.channel
     member_role = discord.utils.get(ctx.guild.roles, name="┆Member")
    
    if member_role:
        await channel.set_permissions(member_role, connect=True)
        
        # Remove lock emoji from the channel name
        if channel.name.startswith("🔒"):
            await channel.edit(name=channel.name[2:].strip())
        
        embed = await create_response_embed("Channel Unlocked", f"{ctx.author.mention} **`unlocked` the channel** 🔓") 
    else:
        embed = await create_response_embed("Error", "The @Member role was not found.")
    
    await ctx.reply(embed=embed, mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='lock')
    @commands.check(is_in_target_category)
    async def lock_command(ctx):
        await lock(ctx)
    
    @bot.command(name='unlock')
    @commands.check(is_in_target_category)
    async def unlock_command(ctx):
        await unlock(ctx) 