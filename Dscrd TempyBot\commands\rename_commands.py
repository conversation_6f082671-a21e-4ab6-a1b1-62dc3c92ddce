import discord
from discord.ext import commands
import time
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category, is_channel_owner_or_manager

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None
rename_usage = None
MAX_RENAMES = None
COOLDOWN_DURATION = None

async def check_rename_limit(ctx):
    """Check if user has exceeded rename limit"""
    user_id = ctx.author.id
    current_time = time.time()
    user_data = rename_usage[user_id]
    
    # Reset count if cooldown period has passed
    if current_time >= user_data['reset_time']:
        user_data['count'] = 0
        user_data['reset_time'] = current_time + COOLDOWN_DURATION
    
    # Check if user has reached the limit
    if user_data['count'] >= MAX_RENAMES:
        time_left = int(user_data['reset_time'] - current_time)
        minutes = time_left // 60
        seconds = time_left % 60
        
        embed = discord.Embed(
            title="❌ Rename Limit Reached",
            description=f"You have used all {MAX_RENAMES} renames. Try again in {minutes}m {seconds}s.",
            color=0xff0000
        )
        await ctx.reply(embed=embed, mention_author=False)
        return True
    
    return False

async def update_rename_count(ctx):
    """Update the rename count for the user"""
    user_id = ctx.author.id
    rename_usage[user_id]['count'] += 1
    
    # Calculate remaining renames
    remaining = MAX_RENAMES - rename_usage[user_id]['count']
    time_left = int(rename_usage[user_id]['reset_time'] - time.time())
    minutes = time_left // 60
    
    return remaining, minutes

async def name(ctx, *, new_name: str):
    if not await is_channel_owner_or_manager(ctx):
        return
    
    # Check existing cooldown (if you have other cooldowns)
    if await check_cooldown(ctx):
        return
    
    # Check rename limit
    if await check_rename_limit(ctx):
        return
   
    channel = ctx.author.voice.channel
    await channel.edit(name=new_name)
    
    # Update rename count and get remaining info
    remaining, reset_minutes = await update_rename_count(ctx)
    
    # Create success embed with remaining renames info
    embed = await create_response_embed(
        "✅ Channel Renamed",
        f"**Channel renamed to** `{new_name}`!\n"
        f"You have `{remaining} rename(s) left` **(resets in {reset_minutes} minutes)**."
    )
    await ctx.reply(embed=embed, mention_author=False)

async def check_renames(ctx):
    """Check how many renames are left"""
    user_id = ctx.author.id
    current_time = time.time()
    user_data = rename_usage[user_id]
    
    # Reset count if cooldown period has passed
    if current_time >= user_data['reset_time']:
        user_data['count'] = 0
        user_data['reset_time'] = current_time + COOLDOWN_DURATION
    
    remaining = MAX_RENAMES - user_data['count']
    time_left = int(user_data['reset_time'] - current_time)
    minutes = time_left // 60
    seconds = time_left % 60
    
    embed = discord.Embed(
        title="🔄 Rename Status",
        description=f"Renames remaining: **{remaining}/{MAX_RENAMES}**\n"
                   f"Resets in: {minutes}m {seconds}s",
        color=0x00ff00 if remaining > 0 else 0xff0000
    )
    await ctx.reply(embed=embed, mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='rename')
    @commands.check(is_in_target_category)
    async def rename_command(ctx, *, new_name: str):
        await name(ctx, new_name=new_name)
    
    @bot.command(name='renames')
    @commands.check(is_in_target_category)
    async def renames_command(ctx):
        await check_renames(ctx) 