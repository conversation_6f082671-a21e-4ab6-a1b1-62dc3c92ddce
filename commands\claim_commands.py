import discord
from discord.ext import commands
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def claim(ctx):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    if not ctx.author.voice or not ctx.author.voice.channel:
        embed = await create_response_embed("Error", "You need to be in a voice channel to claim it!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    channel = ctx.author.voice.channel
    if channel.id not in bot_created_channels:
        embed = await create_response_embed("Error", "This command only works in channels created by the bot!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    owner = channel_owners.get(channel.id)
    if owner and owner in channel.members:
        embed = await create_response_embed(
            "Claim Refused",
            f"👑 {owner.mention} **is the current channel owner**!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    channel_owners[channel.id] = ctx.author
    embed = await create_response_embed(
        "Channel Claimed",
        f"👑 {ctx.author.mention} **Succesfully claimed the channel**!"
    )
    await ctx.reply(embed=embed, mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='claim')
    @commands.check(is_in_target_category)
    async def claim_command(ctx):
        await claim(ctx) 