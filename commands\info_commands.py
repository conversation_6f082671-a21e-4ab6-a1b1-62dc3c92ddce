import discord
from discord.ext import commands
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category, is_channel_owner_or_manager, is_channel_owner

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def info(ctx):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    if not ctx.author.voice or not ctx.author.voice.channel:
        embed = await create_response_embed("Error", "You need to be in a voice channel to use this command!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    channel = ctx.author.voice.channel
    if channel.id not in bot_created_channels:
        embed = await create_response_embed("Error", "Command works only in a `BɅD ZONE | Tempy` VC!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    members_in_voice = '\n'.join([member.mention for member in channel.members]) or "None"
    permitted_members = '\n'.join([member.mention for member, perms in channel.overwrites.items() if isinstance(member, discord.Member) and perms.connect]) or "None"
    rejected_members = '\n'.join([member.mention for member, perms in channel.overwrites.items() if isinstance(member, discord.Member) and perms.connect is False]) or "None"
    
    # Text muted users (members with send_messages explicitly denied)
    text_muted_members = []
    for member, perms in channel.overwrites.items():
        if isinstance(member, discord.Member) and hasattr(perms, 'send_messages') and perms.send_messages is False:
            text_muted_members.append(member.mention)
    text_muted_members = '\n'.join(text_muted_members) or "None"
    
    owner = channel_owners.get(channel.id, "Unknown")
   
    # Retrieve channel managers
    managers = channel_managers.get(channel.id, set())
    managers_list = '\n'.join([manager.mention for manager in managers]) or "None"
   
    # Check if the channel is locked
    member_role = discord.utils.get(ctx.guild.roles, name="┆Member")
    is_locked = False
    if member_role:
        overwrites = channel.overwrites_for(member_role)
        if overwrites.connect is False:
            is_locked = True
   
    embed = discord.Embed(
        title=f"{channel.name} - `Voice Info`",
        color=0x2b2d31
    )
    if bot.user.avatar:
        embed.set_thumbnail(url=bot.user.avatar.url)
    else:
        embed.set_thumbnail(url=bot.user.default_avatar.url)
   
    embed.add_field(name="`Owner`", value=f"{owner.mention if isinstance(owner, discord.Member) else owner}", inline=False)
    embed.add_field(name="`Channel Status`", value="Locked 🔒" if is_locked else "Open", inline=True)
    embed.add_field(name="`Created`", value=f"{discord.utils.format_dt(channel.created_at, 'R')}", inline=True)
    embed.add_field(name="`Channel Limit`", value=str(channel.user_limit), inline=True)
    embed.add_field(name="`In Voice`", value=members_in_voice, inline=False)
    embed.add_field(name="`Managers`", value=managers_list, inline=False)
    embed.add_field(name=" `Permitted Users`", value=permitted_members, inline=False)
    embed.add_field(name="`Rejected Users`", value=rejected_members, inline=False)
    embed.add_field(name="`Text Muted Users`", value=text_muted_members, inline=False)
   
    await ctx.reply(embed=embed, mention_author=False)

async def owner(ctx):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    if not ctx.author.voice or not ctx.author.voice.channel:
        embed = await create_response_embed("Error", "You need to be in a voice channel!")
        await ctx.reply(embed=embed, mention_author=False)
        return

    channel = ctx.author.voice.channel
    if channel.id not in bot_created_channels:
        embed = await create_response_embed("Error", "Command works only in a `BɅD ZONE | Tempy` VC!")
        await ctx.reply(embed=embed, mention_author=False)
        return

    owner = channel_owners.get(channel.id)
    if owner:
        embed = await create_response_embed(
            "Channel Owner",
            f"**The current channel owner is** {owner.mention}"
        )
    else:
        embed = await create_response_embed(
            "Channel Owner",
            "⚠️ This VC has no active owner! Use .v claim to claim ownership."
        )
    await ctx.reply(embed=embed, mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='vcinfo')
    @commands.check(is_in_target_category)
    async def vcinfo_command(ctx):
        await info(ctx)
    
    @bot.command(name='owner')
    @commands.check(is_in_target_category)
    async def owner_command(ctx):
        await owner(ctx) 