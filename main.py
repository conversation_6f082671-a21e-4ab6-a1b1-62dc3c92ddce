import discord
from discord.ext import commands
import time
from collections import defaultdict
import requests
import asyncio

# Bot setup
intents = discord.Intents.default()
intents.message_content = True
intents.members = True
intents.voice_states = True
bot = commands.Bot(command_prefix=".v ", intents=intents)

# Global variables and constants
rename_usage = defaultdict(lambda: {'count': 0, 'reset_time': 0})
channel_owners = {}
channel_managers = {}
bot_created_channels = set()
welcome_messages = {}
user_cooldowns = {}
unknown_command_cooldowns = {}
unknown_command_attempts = defaultdict(int)

# Constants
MAX_RENAMES = 2
COOLDOWN_DURATION = 600
COOLDOWN_THRESHOLD = 4
COOLDOWN_PERIOD = 270
COMMAND_WINDOW = 60
VOICE_LOG_CHANNEL_ID = 1410734378559995934
TARGET_CATEGORY_NAME = "🔊 | ====== Temp Voices ====== | 🔊"
TOKEN = "MTI5ODY0OTQzNjQxMDYxMzg5MQ.GgY02q.fcusPxEfeimbjJrTT0pn1BTIWYsIAH7T6b_O6A"
FOUNDER_ID = 334316627099123713
SERVER_NAME = "𝐃𝐞𝐚𝐭𝐡𝐙𝐨𝐧𝐞 𝐂𝐨𝐦𝐦𝐮𝐧𝐢𝐭𝐲"
UNKNOWN_COMMAND_COOLDOWN = 7200
MAX_UNKNOWN_ATTEMPTS = 4

# Import command modules after bot is created
def import_and_setup_commands():
    """Import command modules and setup global variables"""
    # Import all modules first
    from commands import lock_commands
    from commands import info_commands
    from commands import limit_commands
    from commands import rename_commands
    from commands import permission_commands
    from commands import camera_commands
    from commands import status_commands
    from commands import soundboard_commands
    from commands import chat_commands
    from commands import claim_commands
    from commands import transfer_commands
    from commands import manager_commands
    from commands import request_commands
    from commands import help_commands
    from commands import admin_commands
    from commands import cooldown_utils
    from commands import voice_events
    from commands import ui_components
    
    # Set global variables in each module
    modules = [
        lock_commands, info_commands, limit_commands,
        rename_commands, permission_commands, camera_commands,
        status_commands, soundboard_commands, chat_commands,
        claim_commands, transfer_commands, manager_commands,
        request_commands, help_commands, admin_commands,
        cooldown_utils, voice_events, ui_components
    ]
    
    for module in modules:
        # Set the bot instance first
        if hasattr(module, 'set_bot'):
            module.set_bot(bot)
        else:
            module.bot = bot
            
        # Set other global variables
        if hasattr(module, 'channel_owners') or not hasattr(module, 'set_bot'):
            module.channel_owners = channel_owners
        if hasattr(module, 'channel_managers') or not hasattr(module, 'set_bot'):
            module.channel_managers = channel_managers
        if hasattr(module, 'bot_created_channels') or not hasattr(module, 'set_bot'):
            module.bot_created_channels = bot_created_channels
        if hasattr(module, 'rename_usage') or not hasattr(module, 'set_bot'):
            module.rename_usage = rename_usage
        if hasattr(module, 'user_cooldowns') or not hasattr(module, 'set_bot'):
            module.user_cooldowns = user_cooldowns
        if hasattr(module, 'unknown_command_cooldowns') or not hasattr(module, 'set_bot'):
            module.unknown_command_cooldowns = unknown_command_cooldowns
        if hasattr(module, 'unknown_command_attempts') or not hasattr(module, 'set_bot'):
            module.unknown_command_attempts = unknown_command_attempts
        if hasattr(module, 'TARGET_CATEGORY_NAME') or not hasattr(module, 'set_bot'):
            module.TARGET_CATEGORY_NAME = TARGET_CATEGORY_NAME
        if hasattr(module, 'TOKEN') or not hasattr(module, 'set_bot'):
            module.TOKEN = TOKEN
        if hasattr(module, 'FOUNDER_ID') or not hasattr(module, 'set_bot'):
            module.FOUNDER_ID = FOUNDER_ID
        if hasattr(module, 'SERVER_NAME') or not hasattr(module, 'set_bot'):
            module.SERVER_NAME = SERVER_NAME
        if hasattr(module, 'MAX_RENAMES') or not hasattr(module, 'set_bot'):
            module.MAX_RENAMES = MAX_RENAMES
        if hasattr(module, 'COOLDOWN_DURATION') or not hasattr(module, 'set_bot'):
            module.COOLDOWN_DURATION = COOLDOWN_DURATION
        if hasattr(module, 'COOLDOWN_THRESHOLD') or not hasattr(module, 'set_bot'):
            module.COOLDOWN_THRESHOLD = COOLDOWN_THRESHOLD
        if hasattr(module, 'COOLDOWN_PERIOD') or not hasattr(module, 'set_bot'):
            module.COOLDOWN_PERIOD = COOLDOWN_PERIOD
        if hasattr(module, 'COMMAND_WINDOW') or not hasattr(module, 'set_bot'):
            module.COMMAND_WINDOW = COMMAND_WINDOW
        if hasattr(module, 'VOICE_LOG_CHANNEL_ID') or not hasattr(module, 'set_bot'):
            module.VOICE_LOG_CHANNEL_ID = VOICE_LOG_CHANNEL_ID
        if hasattr(module, 'UNKNOWN_COMMAND_COOLDOWN') or not hasattr(module, 'set_bot'):
            module.UNKNOWN_COMMAND_COOLDOWN = UNKNOWN_COMMAND_COOLDOWN
        if hasattr(module, 'MAX_UNKNOWN_ATTEMPTS') or not hasattr(module, 'set_bot'):
            module.MAX_UNKNOWN_ATTEMPTS = MAX_UNKNOWN_ATTEMPTS
    
    # Register commands with the bot (only call if the function exists)
    command_modules = [
        lock_commands, info_commands, limit_commands,
        rename_commands, permission_commands, camera_commands,
        status_commands, soundboard_commands, chat_commands,
        claim_commands, transfer_commands, manager_commands,
        request_commands, help_commands, admin_commands
    ]
    
    for module in command_modules:
        if hasattr(module, 'register_commands'):
            module.register_commands(bot)

# Unknown command handling functions
def is_user_on_unknown_cooldown(user_id):
    """Check if user is on cooldown for unknown commands"""
    if user_id not in unknown_command_cooldowns:
        return False
    
    current_time = time.time()
    cooldown_end = unknown_command_cooldowns[user_id]
    
    if current_time >= cooldown_end:
        del unknown_command_cooldowns[user_id]
        if user_id in unknown_command_attempts:
            del unknown_command_attempts[user_id]
        return False
    
    return True

def put_user_on_unknown_cooldown(user_id):
    """Put user on cooldown for unknown commands"""
    unknown_command_cooldowns[user_id] = time.time() + UNKNOWN_COMMAND_COOLDOWN
    unknown_command_attempts[user_id] = 0

async def handle_unknown_command(message, command_name):
    """Handle unknown command attempts with cooldown system"""
    user_id = message.author.id
    
    if is_user_on_unknown_cooldown(user_id):
        cooldown_end = unknown_command_cooldowns[user_id]
        time_left = int((cooldown_end - time.time()) / 60)
        
        embed = discord.Embed(
            title="<:time:1257454120739016764> Command Cooldown Active",
            description=f"You are on cooldown for spamming Prefix.\n**Time remaining:** {time_left} minutes",
            color=0xff6b6b
        )
        embed.set_footer(text="Wait 2 hours before using any commands.")
        
        await message.reply(embed=embed, mention_author=False)
        return
    
    unknown_command_attempts[user_id] += 1
    current_attempts = unknown_command_attempts[user_id]
    
    if current_attempts >= MAX_UNKNOWN_ATTEMPTS:
        put_user_on_unknown_cooldown(user_id)
        
        embed = discord.Embed(
            title="<:time:1257454120739016764> Command Cooldown Applied",
            description="You are on cooldown for **2 hours** for spamming bot prefix `.v`.",
            color=0xff4757
        )
        embed.add_field(
            name="📋 How to view commands",
            value="Use `.v cmd` to show the list of available commands",
            inline=False
        )
        embed.add_field(
            name="<:deathzone:1250199513550028956>  !",
            value="Sorry you cannot use **any** command while on cooldown",
            inline=False
        )
        embed.set_footer(text="Try again after 2 hours")
        
        await message.reply(embed=embed, mention_author=False)
        print(f"User {message.author} ({user_id}) got 2-hours cooldown for prefix spam")
    else:
        remaining_attempts = MAX_UNKNOWN_ATTEMPTS - current_attempts
        plural = "s" if remaining_attempts != 1 else ""
        
        embed = discord.Embed(
            title="<:deathzone:1250199513550028956> Oops, Unknown Command",
            description="The command is not in the command list.",
            color=0xffa726
        )
        embed.add_field(
            name="📋 How to view commands ?",
            value="Use `.v cmd` to show available commands",
            inline=False
        )
        embed.add_field(
            name="<:deathzone:1250199513550028956> Warning",
            value=f"Please don't spam bot prefix  `.v` **{remaining_attempts}** time{plural} more gets you a **2-hours cooldown**",
            inline=False
        )
        embed.set_footer(text=f"Attempt {current_attempts}/{MAX_UNKNOWN_ATTEMPTS}")
        
        await message.reply(embed=embed, mention_author=False)
        print(f"Prefix spam: .v {command_name} by {message.author} ({current_attempts}/{MAX_UNKNOWN_ATTEMPTS})")

@bot.event
async def on_message(message):
    if message.author.bot:
        return
    
    if message.content.startswith('.v '):
        if is_user_on_unknown_cooldown(message.author.id):
            cooldown_end = unknown_command_cooldowns[message.author.id]
            time_left = int((cooldown_end - time.time()) / 60)
            
            embed = discord.Embed(
                title="🚫 No commands for you",
                description="Sorry but you cannot use commands while on cooldown.",
                color=0xff4757
            )
            embed.add_field(
                name="<:time:1257454120739016764> Time Remaining",
                value=f"{time_left} minutes",
                inline=True
            )
            embed.add_field(
                name="📋 Reason",
                value="Prefix Spam `.v`",
                inline=True
            )
            embed.set_footer(text="Please wait for cooldown to expire")
            
            await message.reply(embed=embed, mention_author=False)
            return
        
        args = message.content[3:].strip().split()
        if not args:
            return
        
        command_name = args[0].lower()
        
        valid_commands = [
            'lock', 'unlock', 'vcinfo', 'limit', 'rename', 'renames', 'status', 
            'perm', 'reject', 'tmute', 'tunmute', 'man', 'transfer',
            'claim', 'owner', 'request', 'chatdlt', 'sbon', 'sboff', 'cmd', 'manager', 'camon', 'camoff',
            'cldcheck', 'cldclr'
        ]
        
        if command_name not in valid_commands:
            await handle_unknown_command(message, command_name)
            return
    
    await bot.process_commands(message)

@bot.event
async def on_command_error(ctx, error):
    if isinstance(error, commands.CommandNotFound):
        try:
            message_content = ctx.message.content
            attempted_command = message_content.split()[1] if message_content.startswith('.v') else "unknown"
            
            # Import cooldown_utils after bot is created
            from commands import cooldown_utils
            embed = await cooldown_utils.create_response_embed(
                "Command Not Found", 
                f"The command `{attempted_command}` is not unlisted.\n\nUse `.v cmd` to see listed commands."
            )
            await ctx.reply(embed=embed, mention_author=False)
        except Exception as e:
            from commands import cooldown_utils
            embed = await cooldown_utils.create_response_embed(
                "Command Not Found", 
                f"That command is not recognized.\n\nUse `.v cmd` to see a list of available commands."
            )
            await ctx.reply(embed=embed, mention_author=False)
    
    elif isinstance(error, commands.MissingRequiredArgument):
        parameter = error.param.name
        from commands import cooldown_utils
        embed = await cooldown_utils.create_response_embed(
            "Missing Argument", 
            f"The `{parameter}` argument is required for this command."
        )
        await ctx.reply(embed=embed, mention_author=False)
    
    elif isinstance(error, commands.BadArgument):
        from commands import cooldown_utils
        embed = await cooldown_utils.create_response_embed(
            "Invalid Argument", 
            "One or more arguments are invalid. Please check your command syntax."
        )
        await ctx.reply(embed=embed, mention_author=False)
    
    print(f"Command error: {error}")

@bot.event
async def on_ready():
    print(f"Logged in as {bot.user}")
    print("Bot is ready!")

# Setup and run
if __name__ == "__main__":
    import_and_setup_commands()
    
    # Import voice_events and ui_components after setup
    from commands import voice_events
    from commands import ui_components
    
    voice_events.setup_voice_events(bot, channel_owners, channel_managers, bot_created_channels, 
                       TARGET_CATEGORY_NAME, TOKEN, FOUNDER_ID, SERVER_NAME, VOICE_LOG_CHANNEL_ID)
    ui_components.setup_ui_components(bot, channel_owners, channel_managers, bot_created_channels)
    
    bot.run(TOKEN)