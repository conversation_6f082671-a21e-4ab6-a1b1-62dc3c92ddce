import discord
from discord.ext import commands
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category, is_channel_owner

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def manage(ctx, action: str, member: discord.Member = None):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    if action.lower() == "add":
        # Only owner can add managers
        if not await is_channel_owner(ctx):
            return
        
        if not member or member.bot:
            embed = await create_response_embed("Error", "You must mention a valid user to add as a manager!")
            await ctx.reply(embed=embed, mention_author=False)
            return
        
        # Prevent adding the owner as a manager (redundant)
        channel = ctx.author.voice.channel
        owner = channel_owners.get(channel.id)
        if member == owner:
            embed = await create_response_embed("Error", "The channel owner cannot be added as a manager!")
            await ctx.reply(embed=embed, mention_author=False)
            return
        
        if channel.id not in channel_managers:
            channel_managers[channel.id] = set()
        
        channel_managers[channel.id].add(member)
        embed = await create_response_embed("Manager Added", f"👤 {ctx.author.mention} **Added** {member.mention} as manager of this channel!")
        await ctx.reply(embed=embed, mention_author=False)
    
    elif action.lower() == "remove":
        # Only owner can remove managers
        if not await is_channel_owner(ctx):
            return
        
        if not member:
            embed = await create_response_embed("Error", "You must mention a valid user to remove as a manager!")
            await ctx.reply(embed=embed, mention_author=False)
            return
        
        channel = ctx.author.voice.channel
        if channel.id in channel_managers and member in channel_managers[channel.id]:
            channel_managers[channel.id].remove(member)
            embed = await create_response_embed("Manager Removed", f"👤 {member.mention} is no longer a manager of this channel!")
            await ctx.reply(embed=embed, mention_author=False)
        else:
            embed = await create_response_embed("Error", f"{member.mention} is not a manager of this channel!")
            await ctx.reply(embed=embed, mention_author=False)
    
    else:
        embed = await create_response_embed("Error", "Invalid action! Use `.v man add @user` to add or" \
        "`.v man remove @user` to remove a manager.")
        await ctx.reply(embed=embed, mention_author=False)

async def show_managers(ctx):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    
    channel = ctx.author.voice.channel
    
    # Get the channel owner
    owner = channel_owners.get(channel.id)
    owner_text = f"👑 **Owner:** {owner.mention}" if owner else "👑 **Owner:** None"
    
    # Get the managers
    managers = channel_managers.get(channel.id, set())
    
    if managers:
        manager_list = "\n".join([f"👤 {manager.mention}" for manager in managers])
        managers_text = f"**Managers:**\n{manager_list}"
    else:
        managers_text = "**Managers:** None"
    
    # Create the embed
    embed_description = f"{owner_text}\n\n{managers_text}"
    embed = await create_response_embed("Channel Management", embed_description)
    await ctx.reply(embed=embed, mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='man')
    @commands.check(is_in_target_category)
    async def man_command(ctx, action: str, member: discord.Member = None):
        await manage(ctx, action, member)
    
    @bot.command(name='manager')
    @commands.check(is_in_target_category)
    async def manager_command(ctx):
        await show_managers(ctx) 