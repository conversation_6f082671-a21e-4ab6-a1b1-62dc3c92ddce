import discord
from discord.ext import commands
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category, is_channel_owner_or_manager

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def set_limit(ctx, limit: int):
    if not await is_channel_owner_or_manager(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    
    channel = ctx.author.voice.channel
    if limit < 0:
        embed = await create_response_embed("⚠️ Error", "Member limit must be 0 or higher!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    await channel.edit(user_limit=limit)
    embed = await create_response_embed("Channel Limit Updated", f"{ctx.author.mention} Set channel limit to `{limit}`! ")
    await ctx.reply(embed=embed, mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='limit')
    @commands.check(is_in_target_category)
    async def limit_command(ctx, limit: int):
        await set_limit(ctx, limit) 