import discord
from discord import ui
from .cooldown_utils import create_response_embed, is_channel_owner_or_manager, is_channel_owner

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

# Helper function to create a mock context for our existing commands
async def create_mock_context(interaction, command_name, *args):
    class MockContext:
        def __init__(self, interaction, command_name):
            self.author = interaction.user
            self.guild = interaction.guild
            self.channel = interaction.channel
            self.bot = interaction.client
            self.command = command_name
            self.voice_client = None
            self.message = None
            
            # Set up voice client
            if self.author.voice:
                self.voice_client = self.author.voice
            
        async def reply(self, content=None, embed=None, mention_author=False):
            # For commands that don't respond directly through the interaction
            if isinstance(interaction.response, discord.InteractionResponse) and not interaction.response.is_done():
                await interaction.response.send_message(content=content, embed=embed, ephemeral=True)
            else:
                await interaction.followup.send(content=content, embed=embed, ephemeral=True)
                
    ctx = MockContext(interaction, command_name)
    
    # Add send method directly to the instance
    async def send(content=None, embed=None):
        if isinstance(interaction.response, discord.InteractionResponse) and not interaction.response.is_done():
            await interaction.response.send_message(content=content, embed=embed, ephemeral=True)
        else:
            await interaction.followup.send(content=content, embed=embed, ephemeral=True)
    
    ctx.send = send
    
    return ctx

# Function to handle button commands
async def handle_button_command(interaction, command_name):
    # Check if the user is in a voice channel
    if not interaction.user.voice or not interaction.user.voice.channel:
        await interaction.response.send_message("You need to be in a voice channel to use this command!", ephemeral=True)
        return
        
    channel = interaction.user.voice.channel
    
    # Create a mock context for our existing commands
    ctx = await create_mock_context(interaction, command_name)
    
    # Execute the appropriate command
    if command_name == "lock":
        from .lock_commands import lock
        await lock(ctx)
    elif command_name == "unlock":
        from .lock_commands import unlock
        await unlock(ctx)
    elif command_name == "info":
        from .info_commands import info
        await info(ctx)
    
    # Acknowledge the interaction if needed
    if isinstance(interaction.response, discord.InteractionResponse) and not interaction.response.is_done():
        await interaction.response.defer()

# Define custom View with emoji-only buttons for voice channel management
class VoiceControlPanel(ui.View):
    def __init__(self, channel_id):
        super().__init__(timeout=None)  # Make buttons persistent
        self.channel_id = channel_id
    
    @ui.button(emoji="<:Locked:1338041296518123531>", style=discord.ButtonStyle.secondary)
    async def lock_button(self, interaction: discord.Interaction, button: ui.Button):
        await handle_button_command(interaction, "lock")
    
    @ui.button(emoji="<:Unlocked:1338041281296728064>", style=discord.ButtonStyle.secondary)
    async def unlock_button(self, interaction: discord.Interaction, button: ui.Button):
        await handle_button_command(interaction, "unlock")

    @ui.button(emoji="<:OwnerCrown:1338041249025884191>", style=discord.ButtonStyle.secondary)
    async def managers_button(self, interaction: discord.Interaction, button: ui.Button):
        # Open a select menu for manager options
        await interaction.response.send_message("Manager options:", view=ManagerOptionsView(self.channel_id), ephemeral=True)
    
    @ui.button(emoji="ℹ️", style=discord.ButtonStyle.secondary)
    async def info_button(self, interaction: discord.Interaction, button: ui.Button):
        await handle_button_command(interaction, "info")
    
    @ui.button(emoji="<:Checkmark:1338041264565784586>", style=discord.ButtonStyle.secondary)
    async def permit_button(self, interaction: discord.Interaction, button: ui.Button):
        # Open a modal to get the user to permit
        modal = PermitUserModal(self.channel_id)
        await interaction.response.send_modal(modal)
    
    @ui.button(emoji="<:nope:1338041312133386300>", style=discord.ButtonStyle.secondary)
    async def reject_button(self, interaction: discord.Interaction, button: ui.Button):
        # Open a modal to get the user to reject
        modal = RejectUserModal(self.channel_id)
        await interaction.response.send_modal(modal)
    
    @ui.button(emoji="<:GFX:1338041230998638603>", style=discord.ButtonStyle.secondary)
    async def rename_button(self, interaction: discord.Interaction, button: ui.Button):
        # Open a modal to rename the channel
        modal = RenameChannelModal(self.channel_id)
        await interaction.response.send_modal(modal)

# Modals for input collection
class PermitUserModal(ui.Modal, title="Permit User"):
    def __init__(self, channel_id):
        super().__init__()
        self.channel_id = channel_id
        
    user_id = ui.TextInput(label="User ID or @mention", placeholder="Enter user ID or @mention", required=True)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Extract user from input
        user_input = self.user_id.value
        user_id = None
        
        # Handle different input formats
        if user_input.startswith("<@") and user_input.endswith(">"):
            # It's a mention, extract the ID
            user_id = user_input.strip("<@!>")
        else:
            # Assume it's a raw ID
            user_id = user_input
        
        try:
            user_id = int(user_id)
            member = interaction.guild.get_member(user_id)
            
            if member:
                # Create a mock context for our existing permit command
                ctx = await create_mock_context(interaction, "permit", member)
                from .permission_commands import permit
                await permit(ctx, [member])
                await interaction.response.send_message(f"Permitted {member.mention} to join the channel!", ephemeral=True)
            else:
                await interaction.response.send_message("Couldn't find that user. Please make sure they are in the server.", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Invalid user ID or mention. Please try again.", ephemeral=True)

class RejectUserModal(ui.Modal, title="Reject User"):
    def __init__(self, channel_id):
        super().__init__()
        self.channel_id = channel_id
        
    user_id = ui.TextInput(label="User ID or @mention", placeholder="Enter user ID or @mention", required=True)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Extract user from input
        user_input = self.user_id.value
        user_id = None
        
        # Handle different input formats
        if user_input.startswith("<@") and user_input.endswith(">"):
            # It's a mention, extract the ID
            user_id = user_input.strip("<@!>")
        else:
            # Assume it's a raw ID
            user_id = user_input
        
        try:
            user_id = int(user_id)
            member = interaction.guild.get_member(user_id)
            
            if member:
                # Create a mock context for our existing reject command
                ctx = await create_mock_context(interaction, "reject", member)
                from .permission_commands import reject
                await reject(ctx, [member])
                await interaction.response.send_message(f"Rejected {member.mention} from the channel!", ephemeral=True)
            else:
                await interaction.response.send_message("Couldn't find that user. Please make sure they are in the server.", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Invalid user ID or mention. Please try again.", ephemeral=True)

class SetLimitModal(ui.Modal, title="Set Channel Limit"):
    def __init__(self, channel_id):
        super().__init__()
        self.channel_id = channel_id
        
    limit = ui.TextInput(label="User Limit", placeholder="Enter a number (0 for unlimited)", required=True)
    
    async def on_submit(self, interaction: discord.Interaction):
        try:
            limit_value = int(self.limit.value)
            if limit_value < 0:
                await interaction.response.send_message("Limit must be 0 or higher.", ephemeral=True)
                return
                
            # Create a mock context for our existing limit command
            ctx = await create_mock_context(interaction, "limit", limit_value)
            from .limit_commands import limit
            await limit(ctx, limit_value)
            await interaction.response.send_message(f"Channel limit set to {limit_value}!", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Please enter a valid number.", ephemeral=True)

class RenameChannelModal(ui.Modal, title="Rename Channel"):
    def __init__(self, channel_id):
        super().__init__()
        self.channel_id = channel_id
        
    new_name = ui.TextInput(label="New Channel Name", placeholder="Enter a new name for the channel", required=True)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Create a mock context for our existing name command
        ctx = await create_mock_context(interaction, "name", self.new_name.value)
        from .rename_commands import name
        await name(ctx, new_name=self.new_name.value)
        await interaction.response.send_message(f"Channel renamed to {self.new_name.value}!", ephemeral=True)

# Manager options view with buttons
class ManagerOptionsView(ui.View):
    def __init__(self, channel_id):
        super().__init__(timeout=60)  # 1 minute timeout
        self.channel_id = channel_id
    
    @ui.button(label="Add Manager", style=discord.ButtonStyle.success, emoji="➕")
    async def add_manager_button(self, interaction: discord.Interaction, button: ui.Button):
        modal = AddManagerModal(self.channel_id)
        await interaction.response.send_modal(modal)
    
    @ui.button(label="Remove Manager", style=discord.ButtonStyle.danger, emoji="➖")
    async def remove_manager_button(self, interaction: discord.Interaction, button: ui.Button):
        modal = RemoveManagerModal(self.channel_id)
        await interaction.response.send_modal(modal)
    
    @ui.button(label="List Managers", style=discord.ButtonStyle.secondary, emoji="📋")
    async def list_managers_button(self, interaction: discord.Interaction, button: ui.Button):
        channel = interaction.guild.get_channel(self.channel_id)
        if not channel:
            await interaction.response.send_message("Channel not found!", ephemeral=True)
            return
            
        managers = channel_managers.get(self.channel_id, set())
        if not managers:
            await interaction.response.send_message("This channel has no managers.", ephemeral=True)
            return
            
        managers_list = '\n'.join([f"• {manager.mention} ({manager.name})" for manager in managers])
        embed = await create_response_embed("Channel Managers", managers_list)
        await interaction.response.send_message(embed=embed, ephemeral=True)

class AddManagerModal(ui.Modal, title="Add Manager"):
    def __init__(self, channel_id):
        super().__init__()
        self.channel_id = channel_id
        
    user_id = ui.TextInput(label="User ID or @mention", placeholder="Enter user ID or @mention", required=True)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Extract user from input
        user_input = self.user_id.value
        user_id = None
        
        # Handle different input formats
        if user_input.startswith("<@") and user_input.endswith(">"):
            # It's a mention, extract the ID
            user_id = user_input.strip("<@!>")
        else:
            # Assume it's a raw ID
            user_id = user_input
        
        try:
            user_id = int(user_id)
            member = interaction.guild.get_member(user_id)
            
            if member:
                # Create a mock context for our existing man add command
                ctx = await create_mock_context(interaction, "man", "add", member)
                from .manager_commands import manage
                await manage(ctx, "add", member)
                await interaction.response.send_message(f"Added {member.mention} as a manager!", ephemeral=True)
            else:
                await interaction.response.send_message("Couldn't find that user. Please make sure they are in the server.", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Invalid user ID or mention. Please try again.", ephemeral=True)

class RemoveManagerModal(ui.Modal, title="Remove Manager"):
    def __init__(self, channel_id):
        super().__init__()
        self.channel_id = channel_id
        
    user_id = ui.TextInput(label="User ID or @mention", placeholder="Enter user ID or @mention", required=True)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Extract user from input
        user_input = self.user_id.value
        user_id = None
        
        # Handle different input formats
        if user_input.startswith("<@") and user_input.endswith(">"):
            # It's a mention, extract the ID
            user_id = user_input.strip("<@!>")
        else:
            # Assume it's a raw ID
            user_id = user_input
        
        try:
            user_id = int(user_id)
            member = interaction.guild.get_member(user_id)
            
            if member:
                # Create a mock context for our existing man remove command
                ctx = await create_mock_context(interaction, "man", "remove", member)
                from .manager_commands import manage
                await manage(ctx, "remove", member)
                await interaction.response.send_message(f"{ctx.author.mention} **Removed** {member.mention} as a manager!", ephemeral=True)
            else:
                await interaction.response.send_message("Couldn't find that user. Please make sure they are in the server.", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Invalid user ID or mention. Please try again.", ephemeral=True)

def setup_ui_components(bot_instance, channel_owners_dict, channel_managers_dict, bot_created_channels_set):
    """Setup UI components with the provided parameters"""
    global bot, channel_owners, channel_managers, bot_created_channels
    
    bot = bot_instance
    channel_owners = channel_owners_dict
    channel_managers = channel_managers_dict
    bot_created_channels = bot_created_channels_set 