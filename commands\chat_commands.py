import discord
from discord.ext import commands
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category, is_channel_owner_or_manager

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def chat_dlt(ctx, amount: int = None):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    
    # Use existing permission check function
    if not await is_channel_owner_or_manager(ctx):
        return
    
    # Check if amount is specified and valid
    if amount is None or amount <= 0:
        embed = await create_response_embed("Error", "Please specify a valid number of messages to delete (e.g., `.v chatdlt 5`).")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    # Limit to reasonable amount to prevent abuse
    if amount > 100:
        embed = await create_response_embed("Error", "You can only delete up to 100 messages at once.")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    try:
        # Delete messages excluding the command message
        deleted = await ctx.channel.purge(limit=amount, check=lambda msg: msg.id != ctx.message.id)
        
        # Send confirmation as a reply to the command (and keep it visible)
        embed = await create_response_embed("Messages Deleted", f"{ctx.author.mention} **Successfully deleted {len(deleted)} messages**.")
        await ctx.reply(embed=embed, mention_author=False)
        
    except discord.Forbidden:
        embed = await create_response_embed("Error", "I don't have permission to delete messages in this channel.")
        await ctx.reply(embed=embed, mention_author=False)
        
    except discord.HTTPException as e:
        embed = await create_response_embed("Error", f"An error occurred while deleting messages: {str(e)}")
        await ctx.reply(embed=embed, mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='chatdlt')
    @commands.check(is_in_target_category)
    async def chatdlt_command(ctx, amount: int = None):
        await chat_dlt(ctx, amount) 