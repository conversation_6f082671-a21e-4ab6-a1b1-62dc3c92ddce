"""
Professional AI Auto-Aim for Valorant
Advanced enemy detection and smooth mouse tracking
NO FIRING - Only mouse movement to track enemies
"""

import cv2
import numpy as np
import pyautogui
import time
import threading
import ctypes
import math
import random
from collections import deque
import psutil

# Disable pyautogui failsafe
pyautogui.FAILSAFE = False

class ProfessionalAimbot:
    def __init__(self):
        # Screen setup
        self.screen_width, self.screen_height = pyautogui.size()
        self.screen_center_x = self.screen_width // 2
        self.screen_center_y = self.screen_height // 2
        
        # Enemy detection settings
        self.target_color_rgb = (211, 241, 80)  # #d3f150
        self.setup_color_detection()
        
        # AI tracking settings
        self.detection_zone_ratio = 0.8  # Use 80% of screen for detection
        self.setup_detection_zone()
        
        # Mouse movement settings
        self.smoothing_factor = 0.15  # How smooth the aim is (0.1 = very smooth, 0.5 = snappy)
        self.max_move_speed = 15  # Maximum pixels per frame
        self.min_move_threshold = 2  # Minimum distance to move mouse
        
        # Target tracking
        self.current_target = None
        self.target_history = deque(maxlen=10)  # Track last 10 target positions
        self.target_lost_frames = 0
        self.max_lost_frames = 30  # Frames before considering target lost
        
        # Prediction system
        self.enable_prediction = True
        self.prediction_strength = 0.3  # How much to predict ahead
        
        # Humanization
        self.enable_humanization = True
        self.human_error_chance = 0.05  # 5% chance of slight aim error
        self.micro_adjustment_chance = 0.1  # 10% chance of micro adjustments
        
        # Performance
        self.fps_target = 60
        self.frame_time = 1.0 / self.fps_target
        
        # State
        self.is_running = False
        self.targets_tracked = 0
        self.total_movements = 0
        
        print("🎯 Professional AI Aimbot Initialized")
        print(f"Screen: {self.screen_width}x{self.screen_height}")
        print(f"Detection Zone: {self.detection_zone['width']}x{self.detection_zone['height']}")
        print(f"Smoothing: {self.smoothing_factor} | Max Speed: {self.max_move_speed}px/frame")
    
    def setup_color_detection(self):
        """Setup advanced color detection for enemies"""
        rgb_array = np.uint8([[[self.target_color_rgb[2], self.target_color_rgb[1], self.target_color_rgb[0]]]])
        hsv_color = cv2.cvtColor(rgb_array, cv2.COLOR_BGR2HSV)
        
        # More precise color range for better detection
        tolerance = 10
        self.lower_hsv = np.array([
            max(0, hsv_color[0][0][0] - tolerance),
            max(0, hsv_color[0][0][1] - 30),
            max(0, hsv_color[0][0][2] - 30)
        ])
        self.upper_hsv = np.array([
            min(179, hsv_color[0][0][0] + tolerance),
            255,
            255
        ])
    
    def setup_detection_zone(self):
        """Setup the area where we look for enemies"""
        zone_width = int(self.screen_width * self.detection_zone_ratio)
        zone_height = int(self.screen_height * self.detection_zone_ratio)
        
        self.detection_zone = {
            'left': (self.screen_width - zone_width) // 2,
            'top': (self.screen_height - zone_height) // 2,
            'width': zone_width,
            'height': zone_height
        }
    
    def is_valorant_running(self):
        """Check if Valorant is running"""
        try:
            for proc in psutil.process_iter(['name']):
                if proc.info['name'] and 'VALORANT' in proc.info['name'].upper():
                    return True
            return False
        except:
            return True
    
    def capture_screen(self):
        """Capture the detection zone"""
        try:
            screenshot = pyautogui.screenshot(region=(
                self.detection_zone['left'],
                self.detection_zone['top'],
                self.detection_zone['width'],
                self.detection_zone['height']
            ))
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            print(f"Capture error: {e}")
            return None
    
    def detect_enemies(self, frame):
        """Advanced enemy detection using computer vision"""
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Create mask for enemy color
        mask = cv2.inRange(hsv, self.lower_hsv, self.upper_hsv)
        
        # Advanced noise reduction
        kernel = np.ones((3, 3), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        # Find contours (enemy shapes)
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        enemies = []
        for contour in contours:
            area = cv2.contourArea(contour)
            
            # Filter by size (ignore very small detections)
            if area > 50:  # Minimum enemy size
                # Get bounding box
                x, y, w, h = cv2.boundingRect(contour)
                
                # Calculate center point
                center_x = x + w // 2
                center_y = y + h // 2
                
                # Convert back to screen coordinates
                screen_x = center_x + self.detection_zone['left']
                screen_y = center_y + self.detection_zone['top']
                
                enemies.append({
                    'x': screen_x,
                    'y': screen_y,
                    'area': area,
                    'confidence': min(area / 1000.0, 1.0)  # Confidence based on size
                })
        
        # Sort by area (larger enemies first)
        enemies.sort(key=lambda e: e['area'], reverse=True)
        
        return enemies
    
    def select_best_target(self, enemies):
        """AI target selection - choose the best enemy to track"""
        if not enemies:
            return None
        
        current_mouse_x, current_mouse_y = pyautogui.position()
        
        best_target = None
        best_score = -1
        
        for enemy in enemies:
            # Calculate distance from current mouse position
            distance = math.sqrt((enemy['x'] - current_mouse_x)**2 + (enemy['y'] - current_mouse_y)**2)
            
            # Calculate distance from screen center (prefer center targets)
            center_distance = math.sqrt((enemy['x'] - self.screen_center_x)**2 + (enemy['y'] - self.screen_center_y)**2)
            
            # Scoring system (lower distance = higher score)
            distance_score = 1.0 / (1.0 + distance / 100.0)
            center_score = 1.0 / (1.0 + center_distance / 200.0)
            confidence_score = enemy['confidence']
            
            # Combined score
            total_score = distance_score * 0.4 + center_score * 0.3 + confidence_score * 0.3
            
            if total_score > best_score:
                best_score = total_score
                best_target = enemy
        
        return best_target
    
    def predict_target_position(self, target):
        """Predict where the target will be based on movement history"""
        if not self.enable_prediction or len(self.target_history) < 3:
            return target['x'], target['y']
        
        # Calculate velocity from recent positions
        recent_positions = list(self.target_history)[-3:]
        
        if len(recent_positions) >= 2:
            # Calculate average velocity
            vel_x = (recent_positions[-1]['x'] - recent_positions[-2]['x'])
            vel_y = (recent_positions[-1]['y'] - recent_positions[-2]['y'])
            
            # Predict future position
            predicted_x = target['x'] + vel_x * self.prediction_strength
            predicted_y = target['y'] + vel_y * self.prediction_strength
            
            return predicted_x, predicted_y
        
        return target['x'], target['y']
    
    def smooth_mouse_movement(self, target_x, target_y):
        """Smooth, human-like mouse movement to target"""
        current_x, current_y = pyautogui.position()
        
        # Calculate distance to target
        dx = target_x - current_x
        dy = target_y - current_y
        distance = math.sqrt(dx**2 + dy**2)
        
        # Don't move if target is too close
        if distance < self.min_move_threshold:
            return False
        
        # Apply smoothing
        move_x = dx * self.smoothing_factor
        move_y = dy * self.smoothing_factor
        
        # Limit maximum movement speed
        move_distance = math.sqrt(move_x**2 + move_y**2)
        if move_distance > self.max_move_speed:
            scale = self.max_move_speed / move_distance
            move_x *= scale
            move_y *= scale
        
        # Add humanization
        if self.enable_humanization:
            # Random micro-adjustments
            if random.random() < self.micro_adjustment_chance:
                move_x += random.uniform(-0.5, 0.5)
                move_y += random.uniform(-0.5, 0.5)
            
            # Occasional aim errors
            if random.random() < self.human_error_chance:
                error_x = random.uniform(-2, 2)
                error_y = random.uniform(-2, 2)
                move_x += error_x
                move_y += error_y
        
        # Move mouse using Windows API for smoothness
        try:
            user32 = ctypes.windll.user32
            new_x = int(current_x + move_x)
            new_y = int(current_y + move_y)
            user32.SetCursorPos(new_x, new_y)
            self.total_movements += 1
            return True
        except:
            # Fallback to pyautogui
            pyautogui.moveTo(current_x + move_x, current_y + move_y, duration=0)
            self.total_movements += 1
            return True
    
    def main_loop(self):
        """Main AI tracking loop"""
        print("🚀 AI Aimbot Active - Mouse will track enemies")
        print("Press Ctrl+C to stop")
        
        last_frame_time = time.time()
        
        try:
            while self.is_running:
                frame_start = time.time()
                
                # Check if Valorant is running
                if not self.is_valorant_running():
                    time.sleep(0.1)
                    continue
                
                # Capture screen
                frame = self.capture_screen()
                if frame is None:
                    continue
                
                # Detect enemies
                enemies = self.detect_enemies(frame)
                
                if enemies:
                    # Select best target
                    target = self.select_best_target(enemies)
                    
                    if target:
                        self.targets_tracked += 1
                        self.target_lost_frames = 0
                        
                        # Add to history
                        self.target_history.append(target)
                        
                        # Predict target position
                        pred_x, pred_y = self.predict_target_position(target)
                        
                        # Move mouse to target
                        if self.smooth_mouse_movement(pred_x, pred_y):
                            if self.targets_tracked % 100 == 0:  # Print every 100 tracks
                                print(f"🎯 Tracking: {self.targets_tracked} | Movements: {self.total_movements}")
                        
                        self.current_target = target
                else:
                    # No enemies detected
                    self.target_lost_frames += 1
                    
                    if self.target_lost_frames > self.max_lost_frames:
                        self.current_target = None
                        self.target_history.clear()
                
                # Frame rate control
                frame_time = time.time() - frame_start
                sleep_time = max(0, self.frame_time - frame_time)
                time.sleep(sleep_time)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopped by user")
        except Exception as e:
            print(f"❌ Error in main loop: {e}")
    
    def start(self):
        """Start the aimbot"""
        if self.is_running:
            print("Aimbot is already running!")
            return
        
        self.is_running = True
        self.targets_tracked = 0
        self.total_movements = 0
        
        # Run in separate thread
        self.thread = threading.Thread(target=self.main_loop, daemon=True)
        self.thread.start()
        
        try:
            self.thread.join()
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self):
        """Stop the aimbot"""
        self.is_running = False
        
        print(f"\n📊 AI Aimbot Session Stats:")
        print(f"Targets Tracked: {self.targets_tracked}")
        print(f"Mouse Movements: {self.total_movements}")
        if self.targets_tracked > 0:
            avg_movements = self.total_movements / self.targets_tracked
            print(f"Average Movements per Target: {avg_movements:.1f}")
        print("🛑 AI Aimbot stopped")

def setup_interactive_config():
    """Interactive configuration setup"""
    print("🔧 AI Aimbot Configuration Setup")
    print("=" * 40)

    # Playstyle selection
    print("\n1. Select your playstyle:")
    print("1) Smooth Legit (very smooth, human-like)")
    print("2) Balanced (recommended)")
    print("3) Aggressive (faster tracking)")
    print("4) Pro Player (very fast, minimal humanization)")

    style_choice = input("Choose (1-4) [default: 2]: ").strip() or "2"
    styles = {
        "1": "smooth_legit",
        "2": "balanced",
        "3": "aggressive",
        "4": "pro_player"
    }
    playstyle = styles.get(style_choice, "balanced")

    # Sensitivity selection
    print("\n2. Select detection sensitivity:")
    print("1) Very Precise (small detection area, exact color)")
    print("2) Precise (recommended)")
    print("3) Balanced (good for most situations)")
    print("4) Aggressive (wide detection, more forgiving)")

    sens_choice = input("Choose (1-4) [default: 2]: ").strip() or "2"
    sensitivities = {
        "1": "very_precise",
        "2": "precise",
        "3": "balanced",
        "4": "aggressive"
    }
    sensitivity = sensitivities.get(sens_choice, "precise")

    # Weapon selection
    print("\n3. Select primary weapon type:")
    print("1) Rifle (Vandal, Phantom)")
    print("2) Sniper (Operator, Marshal)")
    print("3) SMG (Spectre, Jackal)")
    print("4) Pistol (Ghost, Sheriff)")

    weapon_choice = input("Choose (1-4) [default: 1]: ").strip() or "1"
    weapons = {
        "1": "rifle",
        "2": "sniper",
        "3": "smg",
        "4": "pistol"
    }
    weapon = weapons.get(weapon_choice, "rifle")

    return playstyle, sensitivity, weapon

if __name__ == "__main__":
    print("🤖 Professional AI Aimbot for Valorant")
    print("=" * 50)

    # Configuration options
    print("Configuration Options:")
    print("1) Quick Start (balanced settings)")
    print("2) Custom Configuration")

    config_choice = input("Choose (1-2) [default: 1]: ").strip() or "1"

    if config_choice == "2":
        # Interactive configuration
        try:
            from aimbot_config import create_custom_config
            playstyle, sensitivity, weapon = setup_interactive_config()

            # Create aimbot with custom config
            config = create_custom_config(playstyle, sensitivity, weapon)

            # Apply config to aimbot (we'll need to modify the class to accept config)
            aimbot = ProfessionalAimbot()
            aimbot.smoothing_factor = config.SMOOTHING_FACTOR
            aimbot.max_move_speed = config.MAX_MOVE_SPEED
            aimbot.prediction_strength = config.PREDICTION_STRENGTH
            aimbot.human_error_chance = config.HUMAN_ERROR_CHANCE
            aimbot.micro_adjustment_chance = config.MICRO_ADJUSTMENT_CHANCE

            print(f"\n✅ Configuration Applied:")
            print(f"Playstyle: {playstyle}")
            print(f"Sensitivity: {sensitivity}")
            print(f"Weapon: {weapon}")
            print(f"Smoothing: {config.SMOOTHING_FACTOR}")
            print(f"Max Speed: {config.MAX_MOVE_SPEED} px/frame")

        except ImportError:
            print("⚠️ aimbot_config.py not found, using default settings")
            aimbot = ProfessionalAimbot()
    else:
        # Default configuration
        aimbot = ProfessionalAimbot()
        print("✅ Using balanced configuration")

    print("\n🎯 Features:")
    print("- Advanced enemy detection using computer vision")
    print("- Smooth, human-like mouse tracking")
    print("- Target prediction and selection AI")
    print("- NO FIRING - Only mouse movement")
    print("- Humanization with micro-adjustments")
    print("- 60 FPS tracking performance")
    print("\n🔧 Current Settings:")
    print(f"- Smoothing: {aimbot.smoothing_factor} (0.1=smooth, 0.5=snappy)")
    print(f"- Max Speed: {aimbot.max_move_speed} pixels/frame")
    print(f"- Prediction: {'Enabled' if aimbot.enable_prediction else 'Disabled'}")
    print(f"- Humanization: {'Enabled' if aimbot.enable_humanization else 'Disabled'}")
    print("\n⚠️  Instructions:")
    print("1. Start Valorant")
    print("2. Your mouse will automatically track enemies")
    print("3. You still need to manually fire")
    print("4. Press Ctrl+C to stop")
    print("=" * 50)

    input("Press Enter to start AI aimbot...")
    aimbot.start()
