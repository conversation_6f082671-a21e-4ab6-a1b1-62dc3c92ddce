# Advanced Configuration for Undetected Auto-Fire
# Modify these settings to fine-tune the behavior

class AimConfig:
    # === COLOR DETECTION SETTINGS ===
    TARGET_COLOR_HEX = "#d3f150"  # Valorant enemy highlight color
    COLOR_TOLERANCE = 15  # HSV tolerance for color matching
    DETECTION_THRESHOLD = 100  # Minimum pixels to trigger fire
    
    # === FIRING BEHAVIOR ===
    FIRE_DELAY_MIN = 0.008  # Minimum delay between shots (seconds)
    FIRE_DELAY_MAX = 0.035  # Maximum delay between shots (seconds)
    MAX_CONSECUTIVE_SHOTS = 5  # Prevent spam clicking
    
    # === SCAN SETTINGS ===
    SCAN_DELAY_BASE = 0.001  # Base delay between scans
    SCAN_DELAY_RANDOM = 0.002  # Random additional delay
    
    # === SCREEN CAPTURE ===
    CAPTURE_CENTER_ONLY = True  # Only scan center of screen
    CAPTURE_WIDTH_RATIO = 0.6  # Width ratio of screen to capture
    CAPTURE_HEIGHT_RATIO = 0.6  # Height ratio of screen to capture
    
    # === HUMANIZATION ===
    MICRO_MOVEMENT_CHANCE = 0.25  # Chance of micro-movement before click
    MICRO_MOVEMENT_RANGE = 1  # Pixel range for micro-movements
    JITTER_CHANCE = 0.2  # Chance of adding jitter
    
    # === ANTI-DETECTION ===
    RANDOM_PAUSE_CHANCE = 0.05  # Chance of random pause
    RANDOM_PAUSE_DURATION = (0.1, 0.3)  # Random pause duration range
    METHOD_ROTATION = True  # Rotate between firing methods
    
    # === PERFORMANCE ===
    USE_OPTIMIZED_CAPTURE = True  # Use MSS for faster capture
    NOISE_REDUCTION = True  # Apply morphological operations
    
    # === SAFETY ===
    REQUIRE_VALORANT_ACTIVE = True  # Only work when Valorant is running
    EMERGENCY_STOP_KEY = 'ctrl+shift+q'  # Emergency stop hotkey

# Color conversion helper
def hex_to_rgb(hex_color):
    """Convert hex color to RGB tuple"""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

# Advanced detection zones
DETECTION_ZONES = {
    'center': {'x_ratio': 0.4, 'y_ratio': 0.4, 'width_ratio': 0.2, 'height_ratio': 0.2},
    'crosshair': {'x_ratio': 0.45, 'y_ratio': 0.45, 'width_ratio': 0.1, 'height_ratio': 0.1},
    'full_center': {'x_ratio': 0.25, 'y_ratio': 0.25, 'width_ratio': 0.5, 'height_ratio': 0.5}
}

# Weapon-specific settings
WEAPON_CONFIGS = {
    'rifle': {
        'fire_rate': (0.08, 0.12),  # Slower for rifles
        'burst_mode': False
    },
    'smg': {
        'fire_rate': (0.05, 0.08),  # Faster for SMGs
        'burst_mode': False
    },
    'pistol': {
        'fire_rate': (0.15, 0.25),  # Slower for pistols
        'burst_mode': True
    }
}
