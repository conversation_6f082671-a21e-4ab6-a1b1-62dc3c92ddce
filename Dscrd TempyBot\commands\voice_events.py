import discord
from discord.ext import commands
import requests
from .cooldown_utils import create_response_embed

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None
TARGET_CATEGORY_NAME = None
TOKEN = None
FOUNDER_ID = None
SERVER_NAME = None
VOICE_LOG_CHANNEL_ID = None

# Store message IDs for each channel's log
channel_log_messages = {}  # Format: {channel_id: message_id}
deleted_channel_pages = {}

# Store pagination data for each channel
channel_log_pages = {}  # Format: {channel_id: {"pages": [embed1, embed2...], "current_page": 0}}

class PaginationView(discord.ui.View):
    def __init__(self, channel_id):
        super().__init__(timeout=None)
        self.channel_id = channel_id

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        # only allow certain people to use buttons if needed
        return True

    @discord.ui.button(emoji="◀️", style=discord.ButtonStyle.grey)
    async def previous_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.change_page(interaction, -1)

    @discord.ui.button(emoji="▶️", style=discord.ButtonStyle.grey)
    async def next_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.change_page(interaction, 1)

    async def change_page(self, interaction: discord.Interaction, direction: int):
        # Try normal active channel pages first
        pagination_data = channel_log_pages.get(self.channel_id)

        # If channel is deleted, fallback to deleted_channel_pages
        if not pagination_data:
            pagination_data = deleted_channel_pages.get(self.channel_id)

        if not pagination_data:
            await interaction.response.send_message("No pagination data found.", ephemeral=True)
            return

        pages = pagination_data["pages"]
        current_page = pagination_data["current_page"]

        # calculate new page
        new_page = current_page + direction
        if new_page < 0:
            new_page = 0
        elif new_page >= len(pages):
            new_page = len(pages) - 1

        pagination_data["current_page"] = new_page
        await interaction.response.edit_message(embed=pages[new_page], view=self)

async def update_voice_channel_log(channel, event_type=None, user=None):
    """Properly update the log embed for a voice channel with working pagination"""
    log_channel_id = VOICE_LOG_CHANNEL_ID
    log_channel = channel.guild.get_channel(log_channel_id)
    if not log_channel:
        return

    owner = channel_owners.get(channel.id)
    current_time = discord.utils.utcnow().strftime("%H:%M:%S")

    existing_message = None
    activity_log = []

    # Try to fetch previous activity if exists
    if channel.id in channel_log_pages:
        page_data = channel_log_pages[channel.id]
        # Collect all previous activity logs from all pages
        for embed in page_data["pages"]:
            for field in embed.fields:
                if "Activity Log" in field.name:
                    activity_log.extend(field.value.split('\n'))

    # Build the new event log entry
    new_entry = None
    if event_type:
        if event_type == "create":
            new_entry = f"`{current_time}` 🔨 Channel created by {user.mention}"
        elif event_type == "join":
            new_entry = f"`{current_time}` ➡️ {user.mention} joined the channel"
        elif event_type == "leave":
            new_entry = f"`{current_time}` ⬅️ {user.mention} left the channel"
        elif event_type == "rename":
            new_entry = f"`{current_time}` 🔄 Channel renamed to **{channel.name}**"
        elif event_type == "delete":
            new_entry = f"`{current_time}` 🗑️ Channel deleted"

    # Add the new event if it's not a duplicate
    if new_entry and (new_entry not in activity_log[:10]):  # Check recent 10
        activity_log.insert(0, new_entry)

    # Create pages again by splitting activity log
    entries_per_page = 8
    activity_log_chunks = [activity_log[i:i+entries_per_page] for i in range(0, len(activity_log), entries_per_page)]

    # Create the base embed
    if event_type == "delete":
        base_embed = discord.Embed(
            title=f"{channel.name} - DELETED",
            description="This voice channel has been deleted.",
            color=0xFF5555
        )
        if owner:
            base_embed.add_field(name="👑 Former Owner", value=f"{owner.mention}", inline=True)
            base_embed.set_thumbnail(url=owner.display_avatar.url)
        base_embed.set_footer(text=f"Deleted at: {current_time}")
    else:
        base_embed = discord.Embed(
            title=f"{channel.name} - Voice Info",
            color=0x36393F
        )
        if owner:
            base_embed.add_field(name="👑 Owner", value=f"{owner.mention}", inline=False)
        base_embed.add_field(name="⏳ Created", value=current_time, inline=True)
        base_embed.add_field(name="👥 Channel Limit", value=str(channel.user_limit or "0"), inline=True)

        managers = []
        if channel.id in channel_managers:
            managers = [channel.guild.get_member(uid) for uid in channel_managers[channel.id]]
            managers = [m for m in managers if m]
        managers_text = "\n".join([m.mention for m in managers]) or "None"

        if owner:
            base_embed.set_thumbnail(url=owner.display_avatar.url)

    pages = []

    # Attach first chunk of activity log to base page
    if activity_log_chunks:
        base_embed.add_field(
            name="📝 Activity Log",
            value="\n".join(activity_log_chunks[0]),
            inline=False
        )
    timestamp_info = f"Last updated: {current_time}"
    base_embed.set_footer(text=f"Page 1/{max(1, len(activity_log_chunks))} • {timestamp_info}")
    pages.append(base_embed)

    # Create additional pages if needed
    for i in range(1, len(activity_log_chunks)):
        extra_page = discord.Embed(
            title=f"{channel.name} - Activity Log",
            color=0x36393F
        )
        extra_page.add_field(
            name="📝 Activity Log (continued)",
            value="\n".join(activity_log_chunks[i]),
            inline=False
        )
        extra_page.set_footer(text=f"Page {i+1}/{len(activity_log_chunks)} • {timestamp_info}")
        pages.append(extra_page)

    # Save updated pagination
    channel_log_pages[channel.id] = {
        "pages": pages,
        "current_page": 0
    }

    pagination_view = PaginationView(channel.id)

    # Edit or send the message
    if channel.id in channel_log_messages:
        try:
            msg_id = channel_log_messages[channel.id]
            existing_message = await log_channel.fetch_message(msg_id)
            await existing_message.edit(embed=pages[0], view=pagination_view)
            return existing_message
        except (discord.NotFound, discord.HTTPException):
            pass

    # If no message exists, send a new one
    message = await log_channel.send(embed=pages[0], view=pagination_view)
    channel_log_messages[channel.id] = message.id
    return message

def setup_voice_events(bot_instance, channel_owners_dict, channel_managers_dict, bot_created_channels_set, 
                       target_category_name, token, founder_id, server_name, voice_log_channel_id):
    """Setup voice events with the provided parameters"""
    global bot, channel_owners, channel_managers, bot_created_channels, TARGET_CATEGORY_NAME, TOKEN, FOUNDER_ID, SERVER_NAME, VOICE_LOG_CHANNEL_ID
    
    bot = bot_instance
    channel_owners = channel_owners_dict
    channel_managers = channel_managers_dict
    bot_created_channels = bot_created_channels_set
    TARGET_CATEGORY_NAME = target_category_name
    TOKEN = token
    FOUNDER_ID = founder_id
    SERVER_NAME = server_name
    VOICE_LOG_CHANNEL_ID = voice_log_channel_id

    @bot.event
    async def on_voice_state_update(member, before, after):
        # Handle channel creation
        if after.channel and after.channel.name == "➕・Make Your Room":
            category = after.channel.category
            if category:
                # Create the voice channel
                vc = await category.create_voice_channel(
                    f"{member.display_name}",
                    reason="Voice channel for user interaction"
                )
               
                # Set video quality to full
                await vc.edit(video_quality_mode=discord.VideoQualityMode.full)
               
                # Set base permissions
                overwrites = discord.PermissionOverwrite()
                overwrites.connect = True
                overwrites.manage_channels = False
                overwrites.manage_permissions = False
                await vc.set_permissions(member, overwrite=overwrites)
               
                # Register channel and owner
                channel_owners[vc.id] = member
                bot_created_channels.add(vc.id)

                # Move member to new channel
                await member.move_to(vc)
               
                # Create voice control panel
                from .ui_components import VoiceControlPanel
                control_panel = VoiceControlPanel(vc.id)

                # 👇 Set default voice status here
                url = f"https://discord.com/api/v10/channels/{vc.id}/voice-status"
                headers = {
                    "Authorization": f"Bot {TOKEN}",
                    "Content-Type": "application/json"
                }
                payload = {
                    "status": "**<:deathzone:1250199513550028956> .v status to customize <:deathzone:1250199513550028956>**"
                }
                try:
                    requests.put(url, headers=headers, json=payload)
                except Exception as e:
                    print(f"Failed to set default status: {e}")
               
                # Check if user is a founder and create appropriate welcome message
                if member.id == FOUNDER_ID:
                    # Founder welcome message
                    embed = await create_response_embed(
                        "Tempy voice manager!",
                        f"**Yo `{member.display_name}` As a Founder of** `{SERVER_NAME}`, **you have unlimited usage**\n"
                        "**<:deathzone:1250199513550028956> Use `.v` as prefix !**\n"
                    )
                else:
                    # Regular user welcome message
                    embed = await create_response_embed(
                        "Tempy voice manager!",
                        f"**Yo `{member.display_name}` Manage your voice channel with me.**\n"
                        "**<:deathzone:1250199513550028956>  Use `.v` as prefix !**\n"
                    )
                
                embed.set_footer(text=f"Created for {member.name}", icon_url=member.display_avatar.url)
               
                # Send the message with both embed and view, Using the proper silent parameter 
                await vc.send(content=member.mention, embed=embed, view=control_panel, silent=True)

        # Handle member joining a voice channel - SINGLE MERGED HANDLER
        if after.channel and after.channel.id in bot_created_channels and not before.channel == after.channel:
            # If no log exists yet, create first log (for newly created or first joined channels)
            if after.channel.id not in channel_log_messages:
                await update_voice_channel_log(after.channel, "create", member)
            else:
                await update_voice_channel_log(after.channel, "join", member)
        
        # Handle member leaving a voice channel
        if before.channel and before.channel.id in bot_created_channels and not before.channel == after.channel:
            # Check if channel will be deleted (empty)
            if len(before.channel.members) == 0:
                # Update the existing log to mark the channel as deleted
                # This modifies the existing log instead of creating a new embed
                await update_voice_channel_log(before.channel, "delete", member)
                
                # Cleanup tracking dictionaries
                if before.channel.id in channel_log_pages:
                    # We'll keep the data in deleted_channel_pages for reference
                    # assuming update_voice_channel_log handles this transfer when event_type is "delete"
                    pass
                
                if before.channel.id in channel_owners:
                    del channel_owners[before.channel.id]
                if before.channel.id in channel_managers:
                    del channel_managers[before.channel.id]
                
                bot_created_channels.remove(before.channel.id)
                
                # Delete the channel
                await before.channel.delete()
            else:
                # Channel still has members, just log the leave
                await update_voice_channel_log(before.channel, "leave", member)

    @bot.event
    async def on_guild_channel_update(before, after):
        # Only track updates to bot-created voice channels
        if after.id in bot_created_channels and isinstance(after, discord.VoiceChannel):
            # If name changed, update the log
            if before.name != after.name:
                await update_voice_channel_log(after, "rename", None) 