import discord
from discord.ext import commands
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category, is_channel_owner

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def transfer(ctx, member: discord.Member):
    # Only the owner can transfer ownership
    if not await is_channel_owner(ctx):
        return
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
   
    if not ctx.author.voice or not ctx.author.voice.channel:
        return
   
    channel = ctx.author.voice.channel
    if member.bot:
        embed = await create_response_embed("Error", "You cannot transfer ownership to a bot!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    # Check if the member is in the same voice channel
    if not member.voice or member.voice.channel != channel:
        embed = await create_response_embed("Error", "You can only transfer ownership to users in the same voice channel!")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    # Create confirmation view
    class TransferConfirmView(discord.ui.View):
        def __init__(self):
            super().__init__(timeout=30.0)
            self.confirmed = None
        
        @discord.ui.button(label='Yes', style=discord.ButtonStyle.green, emoji='✅')
        async def confirm_yes(self, interaction: discord.Interaction, button: discord.ui.Button):
            if interaction.user != ctx.author:
                await interaction.response.send_message("Only the channel owner can confirm this action!", ephemeral=True)
                return
            
            self.confirmed = True
            self.stop()
            
            # Transfer ownership
            channel_owners[channel.id] = member
            embed = await create_response_embed(
                "Ownership Transfer",
                f"👑 {ctx.author.mention} **transefered ownership to** {member.mention}!"
            )
            await interaction.response.edit_message(embed=embed, view=None)
        
        @discord.ui.button(label='No', style=discord.ButtonStyle.red, emoji='❌')
        async def confirm_no(self, interaction: discord.Interaction, button: discord.ui.Button):
            if interaction.user != ctx.author:
                await interaction.response.send_message("Only the channel owner can confirm this action!", ephemeral=True)
                return
            
            self.confirmed = False
            self.stop()
            
            embed = await create_response_embed("Transfer Cancelled", "Ownership transfer has been cancelled.")
            await interaction.response.edit_message(embed=embed, view=None)
        
        async def on_timeout(self):
            embed = await create_response_embed("Transfer Timeout", "Ownership transfer has timed out.")
            try:
                await self.message.edit(embed=embed, view=None)
            except:
                pass
    
    # Create confirmation embed and view
    view = TransferConfirmView()
    embed = await create_response_embed(
        "Confirm Ownership Transfer",
        f"Transfer ownership to {member.mention}?\n\nThis action cannot be undone."
    )
    message = await ctx.reply(embed=embed, view=view, mention_author=False)
    view.message = message

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='transfer')
    @commands.check(is_in_target_category)
    async def transfer_command(ctx, member: discord.Member):
        await transfer(ctx, member) 