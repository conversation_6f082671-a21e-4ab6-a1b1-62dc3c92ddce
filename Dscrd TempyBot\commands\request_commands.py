import discord
from discord.ext import commands
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def request(ctx, target=None):
    # Removed the check that prevents users in voice channels from making requests
   
    if await check_cooldown(ctx):
        return
       
    if not target:
        embed = await create_response_embed("Missing Target", "Please mention the channel owner, provide a channel ID, or paste a channel link")
        await ctx.reply(embed=embed, mention_author=False)
        return
    
    owner_channel = None
    channel_owner = None
    
    # Check if target is a Discord channel link
    if isinstance(target, str) and "discord.com/channels/" in target:
        try:
            # Extract channel ID from Discord link
            # Format: https://discord.com/channels/guild_id/channel_id
            parts = target.split("/")
            if len(parts) >= 2:
                channel_id = int(parts[-1])  # Last part is channel ID
                owner_channel = ctx.guild.get_channel(channel_id)
                
                if not owner_channel:
                    embed = await create_response_embed("Invalid Channel", "Channel not found or not accessible")
                    await ctx.reply(embed=embed, mention_author=False)
                    return
                
                # Check if this channel has an owner
                if channel_id in channel_owners:
                    channel_owner = channel_owners[channel_id]
                else:
                    embed = await create_response_embed("Not Found", "This channel doesn't have an active owner")
                    await ctx.reply(embed=embed, mention_author=False)
                    return
            else:
                raise ValueError("Invalid link format")
                
        except (ValueError, IndexError):
            embed = await create_response_embed("Invalid Link", "Please provide a valid Discord channel link")
            await ctx.reply(embed=embed, mention_author=False)
            return
    
    # Check if target is a channel ID (numeric string)
    elif isinstance(target, str) and target.isdigit():
        try:
            channel_id = int(target)
            owner_channel = ctx.guild.get_channel(channel_id)
            
            if not owner_channel:
                embed = await create_response_embed("Invalid Channel", "Channel not found or not accessible")
                await ctx.reply(embed=embed, mention_author=False)
                return
            
            # Check if this channel has an owner
            if channel_id in channel_owners:
                channel_owner = channel_owners[channel_id]
            else:
                embed = await create_response_embed("Not Found", "This channel doesn't have an active owner")
                await ctx.reply(embed=embed, mention_author=False)
                return
                
        except ValueError:
            embed = await create_response_embed("Invalid Input", "Please provide a valid channel ID or mention a user")
            await ctx.reply(embed=embed, mention_author=False)
            return
    
    # If target is a Discord Member (mentioned user)
    elif isinstance(target, discord.Member):
        channel_owner = target
        # Find the channel owned by this member
        for vc_id, owner in channel_owners.items():
            if owner.id == target.id:
                owner_channel = ctx.guild.get_channel(vc_id)
                break
                
        if not owner_channel:
            embed = await create_response_embed("Not Found", f"{target.mention} doesn't own an active voice channel")
            await ctx.reply(embed=embed, mention_author=False)
            return
    
    else:
        # Try to convert string to member if it's not a digit
        try:
            converter = commands.MemberConverter()
            channel_owner = await converter.convert(ctx, str(target))
            
            # Find the channel owned by this member
            for vc_id, owner in channel_owners.items():
                if owner.id == channel_owner.id:
                    owner_channel = ctx.guild.get_channel(vc_id)
                    break
                    
            if not owner_channel:
                embed = await create_response_embed("Not Found", f"{channel_owner.mention} doesn't own an active voice channel")
                await ctx.reply(embed=embed, mention_author=False)
                return
                
        except commands.BadArgument:
            embed = await create_response_embed("Invalid Input", "Please mention a valid user, provide a valid channel ID, or paste a channel link")
            await ctx.reply(embed=embed, mention_author=False)
            return
       
    if ctx.author in owner_channel.members:
        embed = await create_response_embed("Already Inside", "You're already in the voice channel")
        await ctx.reply(embed=embed, mention_author=False)
        return
       
    class RequestAccessView(discord.ui.View):
        def __init__(self, requester):
            super().__init__(timeout=20)
            self.requester = requester
            self.message = None
            self.responded = False  # Track if a response has been given
            
        async def on_timeout(self):
            # Only show timeout message if no response was given
            if self.message and not self.responded:
                for item in self.children:
                    item.disabled = True
                try:
                    await self.message.edit(content="⏳ Request timed out. No response received.", view=self)
                except (discord.NotFound, discord.HTTPException):
                    # Message or channel was deleted, nothing we can do
                    pass
                
        @discord.ui.button(label="✅ Accept", style=discord.ButtonStyle.success)
        async def accept(self, interaction: discord.Interaction, button: discord.ui.Button):
            if interaction.user.id != channel_owner.id:
                try:
                    await interaction.response.send_message("Only the owner can accept requests", ephemeral=True)
                except (discord.NotFound, discord.HTTPException):
                    pass  # Interaction/channel no longer exists
                return
            
            # Mark as responded to prevent timeout override
            self.responded = True
            
            try:
                await owner_channel.set_permissions(self.requester, connect=True)
                await interaction.response.edit_message(content=f"{self.requester.mention} has been granted `Connect perm.` ✅", view=None)
                await ctx.send(f"{self.requester.mention}, you've been granted `Connect perm.` to {owner_channel.name}", delete_after=20)
            except (discord.NotFound, discord.HTTPException):
                # Channel/message was deleted or interaction is invalid - try to notify user if possible
                try:
                    if not interaction.response.is_done():
                        await interaction.response.send_message("❌ Channel no longer exists or an error occurred", ephemeral=True)
                    else:
                        await interaction.followup.send("❌ Channel no longer exists or an error occurred", ephemeral=True)
                except (discord.NotFound, discord.HTTPException):
                    # Even the interaction is completely invalid, nothing we can do
                    pass
            
        @discord.ui.button(label="❌ Reject", style=discord.ButtonStyle.danger)
        async def reject(self, interaction: discord.Interaction, button: discord.ui.Button):
            if interaction.user.id != channel_owner.id:
                try:
                    await interaction.response.send_message("Only the owner can reject requests", ephemeral=True)
                except (discord.NotFound, discord.HTTPException):
                    pass  # Interaction/channel no longer exists
                return
            
            # Mark as responded to prevent timeout override
            self.responded = True
            
            try:
                await owner_channel.set_permissions(self.requester, connect=False)
                await interaction.response.edit_message(content=f"{self.requester.mention} was rejected ❌", view=None)
                await ctx.send(f"{self.requester.mention}, your request to join {owner_channel.name} was denied", delete_after=15)
            except (discord.NotFound, discord.HTTPException):
                # Channel/message was deleted or interaction is invalid - try to notify user if possible
                try:
                    if not interaction.response.is_done():
                        await interaction.response.send_message("❌ Channel no longer exists or an error occurred", ephemeral=True)
                    else:
                        await interaction.followup.send("❌ Channel no longer exists or an error. occurred", ephemeral=True)
                except (discord.NotFound, discord.HTTPException):
                    # Even the interaction is completely invalid, nothing we can do
                    pass
    
    view = RequestAccessView(ctx.author)
    embed = await create_response_embed("Voice Join Request", f"{ctx.author.mention} is requesting to join `{owner_channel.name}`")
    sent_message = await owner_channel.send(content=channel_owner.mention, embed=embed, view=view)
    view.message = sent_message  # Store the sent message in the view
    await ctx.reply(f"✅ Request sent to {channel_owner.mention} ➜", mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name="request")
    @commands.check(is_in_target_category)
    async def request_command(ctx, target=None):
        await request(ctx, target) 