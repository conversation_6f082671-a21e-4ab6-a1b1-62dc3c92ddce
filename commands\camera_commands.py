import discord
from discord.ext import commands
from .cooldown_utils import create_response_embed, is_in_target_category, is_channel_owner_or_manager

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def camon(ctx, members: commands.Greedy[discord.Member]):
    if not await is_channel_owner_or_manager(ctx):
        return
    
    if not members:
        await ctx.reply("Please mention at least one user to enable camera for.", mention_author=False)
        return
   
    # Remove self from the list of members
    members = [member for member in members if member != ctx.author]
   
    if not members:
        await ctx.reply("You cannot modify your own camera permissions.", mention_author=False)
        return
   
    channel = ctx.author.voice.channel
    for member in members:
        await channel.set_permissions(member, stream=True)
   
    mentions = ', '.join(member.mention for member in members)
    embed = await create_response_embed(
        "Cam/Stream Enabled",
        f"{ctx.author.mention} **Granted `Video perm.` for** {mentions}. 📹"
    )
    await ctx.reply(embed=embed, mention_author=False)

async def camoff(ctx, members: commands.Greedy[discord.Member]):
    if not await is_channel_owner_or_manager(ctx):
        return
   
    if not members:
        await ctx.reply("Please mention at least one user to disable camera for.", mention_author=False)
        return
   
    # Remove self from the list of members
    members = [member for member in members if member != ctx.author]
   
    if not members:
        await ctx.reply("You cannot modify your own camera permissions.", mention_author=False)
        return
   
    channel = ctx.author.voice.channel
    disconnected_users = []
    
    for member in members:
        # Remove stream permission first
        await channel.set_permissions(member, stream=False)
        
        # If member is currently in the voice channel and streaming, disconnect them
        if member.voice and member.voice.channel == channel:
            if member.voice.self_stream or member.voice.self_video:
                await member.move_to(None)  # Disconnect from voice
                disconnected_users.append(member)
   
    mentions = ', '.join(member.mention for member in members)
    
    description = f"{ctx.author.mention} **Removed `Video perm.` for** {mentions}. 🚫"
    
    if disconnected_users:
        disconnected_mentions = ', '.join(member.mention for member in disconnected_users)
        description += f"\n\n🔌 **Disconnected users with cam/stream ON:** {disconnected_mentions}"
    
    embed = await create_response_embed(
        "Cam/Stream Disabled",
        description
    )
    await ctx.reply(embed=embed, mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='camon')
    @commands.check(is_in_target_category)
    async def camon_command(ctx, members: commands.Greedy[discord.Member]):
        await camon(ctx, members)
    
    @bot.command(name='camoff')
    @commands.check(is_in_target_category)
    async def camoff_command(ctx, members: commands.Greedy[discord.Member]):
        await camoff(ctx, members) 