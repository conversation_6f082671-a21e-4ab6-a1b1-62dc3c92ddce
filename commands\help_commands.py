import discord
from discord.ext import commands
from .cooldown_utils import check_cooldown, create_response_embed, is_in_target_category

# These will be set by main.py
bot = None
channel_owners = None
channel_managers = None
bot_created_channels = None

async def cmd(ctx):
    # Check cooldown before executing command
    if await check_cooldown(ctx):
        return
    
    embed = await create_response_embed(
        "All Commands Here",
        "**<:deathzone:1250199513550028956>  Use .v as prefix!**\n\n"
        "**⚠️ Channel Controls**\n"
        "`lock` - Locks channel\n"
        "`unlock` - Unlocks channel\n"
        "`vcinfo` - Channel info\n"
        "`limit` - Set limit\n"
        "`rename` - Renames channel\n"
        "`status` - Set channel status\n\n"
        
        "**<:dzperson:1287453555594362962> User Management**\n"
        "`perm` - Permit User\n"
        "`reject` - Reject User\n"
        "`tmute` - Mute text.\n"
        "`tunmute` - Unmutes text.\n"
        "`camoff` - Disable Cam/Stream.\n"
        "`camon` - Enable Cam/Stream.\n"
        "`man add` - Adds man.\n"
        "`man remove` - Removes Man.\n"
        "`manager` - Show managers list.\n\n"
        
        "**<:dzgears:1287453570320302100> Ownership**\n"
        "`transfer` - Transfer owner\n"
        "`claim` - Claim channel\n"
        "`owner` - Show owner\n\n"
        
        "**🔧 Misc**\n"
        "`request` - Request joining\n"
        "`renames` - Check renaming\n"
        "`chatdlt` - Clear chat\n"
        "`sbon` - Soundboard on\n"
        "`sboff` - Soundboard off\n\n"

        "** <:deathzone:1250199513550028956> Prime (Admin Only)**\n"
        "`cldcheck` - Show .v cooldowns.\n"
        "`cldclr` - Clear .v cooldown. \n\n"
    )
    
    await ctx.reply(embed=embed, mention_author=False)

def register_commands(bot_instance):
    """Register commands with the bot"""
    global bot
    bot = bot_instance
    
    @bot.command(name='cmd')
    @commands.check(is_in_target_category)
    async def cmd_command(ctx):
        await cmd(ctx) 